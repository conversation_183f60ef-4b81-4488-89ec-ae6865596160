<?php

namespace Tests\Feature;

use App\Models\Field;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(\App\Http\Controllers\Admin\FieldController::class)]
class AdminFieldViewTest extends TestCase
{
    use RefreshDatabase;

    protected $admin;

    protected $field;

    protected function setUp(): void
    {
        parent::setUp();

        // Create admin user
        $this->admin = User::factory()->create(['role' => 'admin']);

        // Create a test field with comprehensive data
        $this->field = Field::factory()->create([
            'name' => 'Test Soccer Field A',
            'type' => 'Soccer',
            'description' => 'Professional soccer field with artificial turf and LED lighting',
            'hourly_rate' => 75.00,
            'capacity' => 22,
            'status' => 'Active',
            'opening_time' => '08:00',
            'closing_time' => '22:00',
        ]);
    }

    #[Test]
    public function admin_can_access_fields_index_page()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('admin.fields.index'));

        $response->assertStatus(200);
        $response->assertSee('Field Management');
        $response->assertSee($this->field->name);
        $response->assertSee('View');
        $response->assertSee('Edit');

        echo "✅ Admin fields index page loads successfully\n";
        echo "✅ Field listing displays correctly\n";
        echo "✅ View and Edit buttons are present\n";
    }

    #[Test]
    public function admin_can_view_field_details_page()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('admin.fields.show', $this->field));

        $response->assertStatus(200);

        // Check basic information
        $response->assertSee($this->field->name);
        $response->assertSee($this->field->type);
        $response->assertSee($this->field->description);
        $response->assertSee($this->field->status);

        // Check pricing and capacity
        $response->assertSee('XCG '.number_format($this->field->hourly_rate, 2));
        $response->assertSee($this->field->capacity.' people');

        // Check navigation buttons
        $response->assertSee('Edit Field');
        $response->assertSee('Delete Field');
        $response->assertSee('Back to Fields');

        echo "✅ Field show page loads successfully\n";
        echo "✅ All field information displays correctly\n";
        echo "✅ Navigation buttons are present\n";
    }

    #[Test]
    public function field_show_page_displays_amenities_correctly()
    {
        // Create some amenities and attach them to the field
        $amenity1 = \App\Models\Amenity::factory()->create(['name' => 'Lighting', 'is_active' => true]);
        $amenity2 = \App\Models\Amenity::factory()->create(['name' => 'Parking', 'is_active' => true]);
        $this->field->amenities()->attach([$amenity1->id, $amenity2->id]);

        $response = $this->actingAs($this->admin)
            ->get(route('admin.fields.show', $this->field));

        $response->assertStatus(200);

        // Check amenities section (feature removed)
        $response->assertDontSee('Available Amenities');

        // The amenities should NOT be displayed (feature removed)
        $response->assertDontSee('Lighting');
        $response->assertDontSee('Parking');

        echo "✅ Amenities section displays correctly\n";
    }

    #[Test]
    public function field_show_page_displays_statistics()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('admin.fields.show', $this->field));

        $response->assertStatus(200);

        // Check for statistics/timestamps
        $response->assertSee('Created');
        $response->assertSee('Last Updated');

        echo "✅ Field statistics display correctly\n";
    }

    #[Test]
    public function field_show_page_has_proper_layout()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('admin.fields.show', $this->field));

        $response->assertStatus(200);

        // Check for admin layout elements
        $response->assertSee('Field Details');
        $response->assertSee('breadcrumb');

        // Should not see any layout errors
        $response->assertDontSee('View [layouts.app] not found');
        $response->assertDontSee('x-app-layout');

        echo "✅ Admin layout loads correctly\n";
        echo "✅ No layout errors detected\n";
    }

    #[Test]
    public function view_field_button_navigation_works()
    {
        // First, access the index page
        $indexResponse = $this->actingAs($this->admin)
            ->get(route('admin.fields.index'));

        $indexResponse->assertStatus(200);

        // Then test the show route directly (simulating clicking the View button)
        $showResponse = $this->actingAs($this->admin)
            ->get(route('admin.fields.show', $this->field));

        $showResponse->assertStatus(200);
        $showResponse->assertSee($this->field->name);

        echo "✅ View Field button navigation works correctly\n";
    }

    #[Test]
    public function edit_field_button_navigation_works()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('admin.fields.edit', $this->field));

        $response->assertStatus(200);
        $response->assertSee('Edit Field');
        $response->assertSee($this->field->name);

        echo "✅ Edit Field button navigation works correctly\n";
    }
}
