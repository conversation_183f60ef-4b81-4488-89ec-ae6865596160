@extends('layouts.admin')

@section('title', 'Create New Field - Field Management System')

@section('content')
    <!-- <PERSON> Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <h1 class="page-title fw-semibold fs-18 mb-0">Create New Field</h1>
        <div class="ms-md-1 ms-0">
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.fields.index') }}">Fields</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Create</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- <PERSON> Header Close -->

    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header">
                    <div class="card-title">
                        Create New Field
                    </div>
                    <div class="card-options">
                        <a href="{{ route('admin.fields.index') }}" class="btn btn-light btn-sm">
                            <i class="ri-arrow-left-line me-1"></i>Back to Fields
                        </a>
                    </div>
                </div>
                <div class="card-body">

                    <form method="POST" action="{{ route('admin.fields.store') }}">
                        @csrf

                        <div class="row">
                            <!-- Basic Information -->
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Basic Information</h5>
                                    </div>
                                    <div class="card-body">
                                        <!-- Field Name -->
                                        <div class="mb-3">
                                            <label for="name" class="form-label">Field Name <span
                                                    class="text-danger">*</span></label>
                                            <input type="text" name="name" id="name" value="{{ old('name') }}"
                                                required class="form-control @error('name') is-invalid @enderror"
                                                placeholder="e.g., Soccer Field A">
                                            @error('name')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <!-- Field Type -->
                                        <div class="mb-3">
                                            <label for="type" class="form-label">Field Type <span
                                                    class="text-danger">*</span></label>
                                            <select name="type" id="type" required
                                                class="form-select @error('type') is-invalid @enderror">
                                                <option value="">Select Field Type</option>
                                                @foreach (\App\Models\Field::getFieldTypes() as $key => $type)
                                                    <option value="{{ $key }}"
                                                        {{ old('type') === $key ? 'selected' : '' }}>{{ $type }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            @error('type')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <!-- Description -->
                                        <div class="mb-3">
                                            <label for="description" class="form-label">Description</label>
                                            <textarea name="description" id="description" rows="3"
                                                class="form-control @error('description') is-invalid @enderror"
                                                placeholder="Describe the field features and specifications...">{{ old('description') }}</textarea>
                                            @error('description')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Pricing and Capacity -->
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Pricing & Capacity</h5>
                                    </div>
                                    <div class="card-body">

                                        <!-- Day Hourly Rate -->
                                        <div class="mb-3">
                                            <label for="hourly_rate" class="form-label">Day Hourly Rate (XCG) <span
                                                    class="text-danger">*</span></label>
                                            <input type="number" name="hourly_rate" id="hourly_rate"
                                                value="{{ old('hourly_rate') }}" step="0.01" min="0"
                                                max="9999.99" required
                                                class="form-control @error('hourly_rate') is-invalid @enderror"
                                                placeholder="0.00">
                                            @error('hourly_rate')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <!-- Night Hourly Rate -->
                                        <div class="mb-3">
                                            <label for="night_hourly_rate" class="form-label">Night Hourly Rate
                                                (XCG)</label>
                                            <input type="number" name="night_hourly_rate" id="night_hourly_rate"
                                                value="{{ old('night_hourly_rate') }}" step="0.01" min="0"
                                                max="9999.99"
                                                class="form-control @error('night_hourly_rate') is-invalid @enderror"
                                                placeholder="0.00">
                                            @error('night_hourly_rate')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <div class="form-text">Leave blank to use day rate for all hours</div>
                                        </div>

                                        <!-- Night Time Start -->
                                        <div class="mb-3">
                                            <label for="night_time_start" class="form-label">Night Time Starts At</label>
                                            <input type="time" name="night_time_start" id="night_time_start"
                                                value="{{ old('night_time_start', '18:00') }}"
                                                class="form-control @error('night_time_start') is-invalid @enderror">
                                            @error('night_time_start')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <div class="form-text">Time when night rate pricing begins (default: 6:00 PM)
                                            </div>
                                        </div>

                                        <!-- Capacity -->
                                        <div class="mb-3">
                                            <label for="capacity" class="form-label">Maximum Capacity <span
                                                    class="text-danger">*</span></label>
                                            <input type="number" name="capacity" id="capacity"
                                                value="{{ old('capacity') }}" min="1" max="1000" required
                                                class="form-control @error('capacity') is-invalid @enderror"
                                                placeholder="Number of people">
                                            @error('capacity')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <!-- Status -->
                                        <div class="mb-3">
                                            <label for="status" class="form-label">Status <span
                                                    class="text-danger">*</span></label>
                                            <select name="status" id="status" required
                                                class="form-select @error('status') is-invalid @enderror">
                                                @foreach (\App\Models\Field::getStatuses() as $key => $status)
                                                    <option value="{{ $key }}"
                                                        {{ old('status', 'Active') === $key ? 'selected' : '' }}>
                                                        {{ $status }}</option>
                                                @endforeach
                                            </select>
                                            @error('status')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Amenities -->
                        <!--<div class="row mt-3">
                                    <div class="col-12">
                                        <div class="card">
                                            <div class="card-header">
                                                <h5 class="card-title mb-0">Available Amenities</h5>
                                            </div>
                                            <div class="card-body">
                                                <div class="row">
                                                    @forelse(\App\Models\Amenity::active()->orderBy('name')->get() as $amenity)
    <div class="col-md-3 col-sm-6">
                                                            <div class="form-check">
                                                                <input type="checkbox" name="amenities[]"
                                                                    value="{{ $amenity->id }}"
                                                                    {{ in_array($amenity->id, old('amenities', [])) ? 'checked' : '' }}
                                                                    class="form-check-input" id="amenity_{{ $amenity->id }}">
                                                                <label class="form-check-label"
                                                                    for="amenity_{{ $amenity->id }}">
                                                                    <i
                                                                        class="{{ $amenity->icon_class }} me-1"></i>{{ $amenity->name }}
                                                                </label>
                                                            </div>
                                                        </div>
                                            @empty
                                                        <div class="col-12">
                                                            <div class="alert alert-info">
                                                                <p class="mb-0">No amenities available. <a
                                                                        href="{{ route('admin.amenities.create') }}">Create
                                                                        some
                                                                        amenities</a> first.</p>
                                                            </div>
                                                        </div>
    @endforelse
                                                </div>
                                                @error('amenities')
        <div class="text-danger mt-2">{{ $message }}</div>
    @enderror
                                            </div>
                                        </div>
                                    </div>
                                </div>-->

                        <!-- Utilities -->
                        <!--<div class="row mt-3">
                                <div class="col-12">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5 class="card-title mb-0">Available Utilities</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                @forelse(\App\Models\Utility::active()->orderBy('name')->get() as $utility)
    <div class="col-md-3 col-sm-6">
                                                        <div class="form-check">
                                                            <input type="checkbox" name="utilities[]"
                                                                value="{{ $utility->id }}"
                                                                {{ in_array($utility->id, old('utilities', [])) ? 'checked' : '' }}
                                                                class="form-check-input" id="utility_{{ $utility->id }}">
                                                            <label class="form-check-label"
                                                                for="utility_{{ $utility->id }}">
                                                                <i
                                                                    class="{{ $utility->icon_class ?? 'ri-tools-line' }} me-1"></i>{{ $utility->name }}
                                                            </label>
                                                        </div>
                                                    </div>
                                            @empty
                                                    <div class="col-12">
                                                        <div class="alert alert-info">
                                                            <p class="mb-0">No utilities available. <a
                                                                    href="{{ route('admin.utilities.create') }}">Create
                                                                    some
                                                                    utilities</a> first.</p>
                                                        </div>
                                                    </div>
    @endforelse
                                            </div>
                                            @error('utilities')
        <div class="text-danger mt-2">{{ $message }}</div>
    @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>-->

                        <!-- Form Actions -->
                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <div class="d-flex gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="ri-save-line me-1"></i>Create Field
                                        </button>
                                        <button type="reset" class="btn btn-secondary">
                                            <i class="ri-refresh-line me-1"></i>Reset Form
                                        </button>
                                    </div>
                                    <a href="{{ route('admin.fields.index') }}" class="btn btn-outline-secondary">
                                        <i class="ri-close-line me-1"></i>Cancel
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Form validation
            const form = document.querySelector('form');
            form.addEventListener('submit', function(e) {
                const name = document.getElementById('name').value.trim();
                const type = document.getElementById('type').value.trim();
                const hourlyRate = document.getElementById('hourly_rate').value.trim();
                const capacity = document.getElementById('capacity').value.trim();

                if (!name) {
                    e.preventDefault();
                    alert('Please enter a field name.');
                    document.getElementById('name').focus();
                    return;
                }

                if (!type) {
                    e.preventDefault();
                    alert('Please select a field type.');
                    document.getElementById('type').focus();
                    return;
                }

                if (!hourlyRate) {
                    e.preventDefault();
                    alert('Please enter an hourly rate.');
                    document.getElementById('hourly_rate').focus();
                    return;
                }

                if (!capacity) {
                    e.preventDefault();
                    alert('Please enter the field capacity.');
                    document.getElementById('capacity').focus();
                    return;
                }
            });

            // Auto-focus on name field
            document.getElementById('name').focus();
        });
    </script>
@endpush
