<?php

require_once 'vendor/autoload.php';

use App\Models\Field;
use App\Models\User;
use App\Models\Amenity;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class DebugAmenityTest extends TestCase
{
    use RefreshDatabase;

    public function test_debug_admin_fields_show()
    {
        $admin = User::factory()->create(['role' => 'admin']);
        $field = Field::factory()->create(['name' => 'Debug Test Field']);
        
        $amenity = Amenity::factory()->create([
            'name' => 'WiFi',
            'icon_class' => 'ri-wifi-line',
            'is_active' => true,
        ]);
        
        $field->amenities()->attach($amenity->id);
        
        $response = $this->actingAs($admin)
            ->get(route('admin.fields.show', $field));
            
        $content = $response->getContent();
        
        echo "=== SEARCHING FOR 'Available Amenities' ===\n";
        $pos = strpos($content, 'Available Amenities');
        if ($pos !== false) {
            echo "FOUND at position: $pos\n";
            echo "Context: " . substr($content, max(0, $pos - 100), 200) . "\n";
        } else {
            echo "NOT FOUND\n";
        }
        
        echo "\n=== SEARCHING FOR 'Amenities' ===\n";
        $positions = [];
        $offset = 0;
        while (($pos = strpos($content, 'Amenities', $offset)) !== false) {
            $positions[] = $pos;
            $offset = $pos + 1;
        }
        
        foreach ($positions as $pos) {
            echo "Found 'Amenities' at position $pos\n";
            echo "Context: " . substr($content, max(0, $pos - 50), 100) . "\n\n";
        }
    }
}
