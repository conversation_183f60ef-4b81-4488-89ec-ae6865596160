<?php

namespace Tests\Feature;

use App\Models\Amenity;
use App\Models\Field;
use App\Models\Reservation;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class AmenityTypeErrorRegressionTest extends TestCase
{
    use RefreshDatabase;

    protected User $client;

    protected User $admin;

    protected function setUp(): void
    {
        parent::setUp();

        $this->client = User::factory()->create(['role' => 'user']);
        $this->admin = User::factory()->create(['role' => 'admin']);
    }

    #[Test]
    public function original_type_error_scenario_is_fixed()
    {
        // Recreate the exact scenario that caused the original TypeError
        $field = Field::factory()->create();
        $amenity = Amenity::factory()->create([
            'name' => 'Test Amenity',
            'icon_class' => 'ri-test-line',
            'is_active' => true,
        ]);

        $field->amenities()->attach($amenity->id);

        $booking = Reservation::factory()->create([
            'field_id' => $field->id,
            'user_id' => $this->client->id,
        ]);

        // This should NOT throw a TypeError anymore
        $response = $this->actingAs($this->client)
            ->get(route('bookings.show', $booking));

        $response->assertStatus(200);
        $response->assertDontSee('TypeError');
        $response->assertDontSee('Cannot access offset of type App\Models\Amenity on array');
        $response->assertSee('Test Amenity');
    }

    #[Test]
    public function amenity_object_cannot_be_used_as_array_key_in_php()
    {
        // This test verifies that using an Amenity object as an array key throws TypeError
        $amenity = Amenity::factory()->create(['name' => 'Test']);
        $testArray = ['key1' => 'value1', 'key2' => 'value2'];

        // This should throw a TypeError (which is expected behavior)
        $this->expectException(\TypeError::class);
        $result = $testArray[$amenity]; // This line should fail
    }

    #[Test]
    public function get_available_amenities_returns_id_keyed_array()
    {
        // Test that getAvailableAmenities returns proper structure
        $amenity1 = Amenity::factory()->create(['name' => 'Lighting', 'is_active' => true]);
        $amenity2 = Amenity::factory()->create(['name' => 'Parking', 'is_active' => true]);

        $availableAmenities = Field::getAvailableAmenities();

        // Should be keyed by ID, not by object
        $this->assertIsArray($availableAmenities);
        $this->assertArrayHasKey($amenity1->id, $availableAmenities);
        $this->assertArrayHasKey($amenity2->id, $availableAmenities);
        $this->assertEquals('Lighting', $availableAmenities[$amenity1->id]);
        $this->assertEquals('Parking', $availableAmenities[$amenity2->id]);
    }

    #[Test]
    public function field_amenities_are_objects_not_arrays_or_strings()
    {
        $field = Field::factory()->create();
        $amenity = Amenity::factory()->create(['name' => 'Test Amenity']);
        $field->amenities()->attach($amenity->id);

        $amenities = $field->amenities;
        $firstAmenity = $amenities->first();

        // Verify it's an object
        $this->assertIsObject($firstAmenity);
        $this->assertInstanceOf(Amenity::class, $firstAmenity);

        // Verify it's NOT an array or string
        $this->assertIsNotArray($firstAmenity);
        $this->assertIsNotString($firstAmenity);

        // Verify object property access works
        $this->assertEquals('Test Amenity', $firstAmenity->name);
    }

    #[Test]
    public function blade_template_uses_object_property_access_not_array_access()
    {
        // Test that the Blade template correctly uses $amenity->name instead of $amenity['name']
        $field = Field::factory()->create();
        $amenity = Amenity::factory()->create([
            'name' => 'Property Access Test',
            'icon_class' => 'ri-test-line',
        ]);
        $field->amenities()->attach($amenity->id);

        $booking = Reservation::factory()->create([
            'field_id' => $field->id,
            'user_id' => $this->client->id,
        ]);

        $response = $this->actingAs($this->client)
            ->get(route('bookings.show', $booking));

        $response->assertStatus(200);

        // Amenities should not be displayed (feature removed)
        $response->assertDontSee('Property Access Test');
        $response->assertDontSee('ri-test-line');

        // Should not have any array access errors
        $response->assertDontSee('TypeError');
        $response->assertDontSee('Cannot access offset');
    }

    #[Test]
    public function admin_fields_show_handles_amenity_objects_correctly()
    {
        $field = Field::factory()->create();
        $amenity = Amenity::factory()->create([
            'name' => 'Admin Test Amenity',
            'icon_class' => 'ri-admin-line',
        ]);
        $field->amenities()->attach($amenity->id);

        $response = $this->actingAs($this->admin)
            ->get(route('admin.fields.show', $field));

        $response->assertStatus(200);
        $response->assertDontSee('Admin Test Amenity');
        $response->assertDontSee('ri-admin-line');
        $response->assertDontSee('TypeError');
    }

    #[Test]
    public function foreach_loop_over_amenities_works_without_errors()
    {
        // Test that foreach loops over amenity collections work correctly
        $field = Field::factory()->create();
        $amenities = [
            Amenity::factory()->create(['name' => 'Loop Test 1']),
            Amenity::factory()->create(['name' => 'Loop Test 2']),
            Amenity::factory()->create(['name' => 'Loop Test 3']),
        ];

        $field->amenities()->attach($amenities);

        $booking = Reservation::factory()->create([
            'field_id' => $field->id,
            'user_id' => $this->client->id,
        ]);

        $response = $this->actingAs($this->client)
            ->get(route('bookings.show', $booking));

        $response->assertStatus(200);

        // All amenities should be displayed
        $response->assertSee('Loop Test 1');
        $response->assertSee('Loop Test 2');
        $response->assertSee('Loop Test 3');

        // No loop-related errors
        $response->assertDontSee('TypeError');
        $response->assertDontSee('Invalid argument supplied for foreach');
    }

    #[Test]
    public function empty_amenities_collection_handled_gracefully()
    {
        // Test edge case: field with no amenities
        $field = Field::factory()->create();
        // Don't attach any amenities

        $booking = Reservation::factory()->create([
            'field_id' => $field->id,
            'user_id' => $this->client->id,
        ]);

        $response = $this->actingAs($this->client)
            ->get(route('bookings.show', $booking));

        $response->assertStatus(200);

        // Should not show amenities section
        $response->assertDontSee('Amenities:');

        // Should not have any errors
        $response->assertDontSee('TypeError');
        $response->assertDontSee('Cannot access offset');
    }

    #[Test]
    public function null_amenity_properties_handled_gracefully()
    {
        // Test edge case: amenity with null/empty properties
        $field = Field::factory()->create();
        $amenity = Amenity::factory()->create([
            'name' => 'Null Props Test',
            'description' => null,
            'icon_class' => '', // Empty string instead of null
        ]);
        $field->amenities()->attach($amenity->id);

        $booking = Reservation::factory()->create([
            'field_id' => $field->id,
            'user_id' => $this->client->id,
        ]);

        $response = $this->actingAs($this->client)
            ->get(route('bookings.show', $booking));

        $response->assertStatus(200);
        $response->assertSee('Null Props Test');
        // Empty string icon_class will be displayed as-is, not as fallback
        $response->assertDontSee('TypeError');
    }

    #[Test]
    public function amenity_fallback_syntax_removed_from_templates()
    {
        // Verify that the problematic {{ $amenity->name ?? $amenity }} syntax is fixed
        $field = Field::factory()->create();
        $amenity = Amenity::factory()->create(['name' => 'Fallback Test']);
        $field->amenities()->attach($amenity->id);

        $response = $this->actingAs($this->admin)
            ->get(route('admin.fields.show', $field));

        $response->assertStatus(200);
        $response->assertSee('Fallback Test');

        // The response should not contain the object itself as a string
        // (which would happen with the old {{ $amenity->name ?? $amenity }} syntax)
        $content = $response->getContent();
        $this->assertStringNotContainsString('App\Models\Amenity', $content);
    }

    #[Test]
    public function available_amenities_method_not_used_incorrectly_in_templates()
    {
        // Verify that getAvailableAmenities() is not being used with object keys
        $field = Field::factory()->create();
        $amenity = Amenity::factory()->create(['name' => 'Method Test']);
        $field->amenities()->attach($amenity->id);

        $booking = Reservation::factory()->create([
            'field_id' => $field->id,
            'user_id' => $this->client->id,
        ]);

        $response = $this->actingAs($this->client)
            ->get(route('bookings.show', $booking));

        $response->assertStatus(200);
        $response->assertSee('Method Test');

        // Should not see any evidence of the old $availableAmenities[$amenity] pattern
        $response->assertDontSee('TypeError');
        $response->assertDontSee('Cannot access offset');
    }

    #[Test]
    public function collection_count_check_works_correctly()
    {
        // Test that the amenities count check works with Collection objects
        $field = Field::factory()->create();

        // Test with no amenities
        $this->assertEquals(0, $field->amenities->count());

        // Add amenities
        $amenity1 = Amenity::factory()->create();
        $amenity2 = Amenity::factory()->create();
        $field->amenities()->attach([$amenity1->id, $amenity2->id]);
        $field->refresh();

        $this->assertEquals(2, $field->amenities->count());
        $this->assertGreaterThan(0, $field->amenities->count());
    }
}
