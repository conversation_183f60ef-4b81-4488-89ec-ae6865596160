@extends('layouts.admin')

@section('title', 'New Reservation - FPMP Sport Park')

@section('content')
    <!-- <PERSON> Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <h1 class="page-title fw-semibold fs-18 mb-0">New Reservation</h1>
        <div class="ms-md-1 ms-0">
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('reservations.index') }}">Reservations</a></li>
                    <li class="breadcrumb-item active" aria-current="page">New</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- <PERSON> Header Close -->

    <!-- Success/Error Messages -->
    @if (session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="ti ti-check-circle me-2"></i>{{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if (session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="ti ti-exclamation-triangle me-2"></i>{{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <!-- Reservation Form -->
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header justify-content-between">
                    <div class="card-title">
                        <i class="ti ti-calendar-plus me-2"></i>Create New Reservation
                    </div>
                    <div class="d-flex gap-2">
                        <a href="{{ route('reservations.index') }}" class="btn btn-secondary btn-sm">
                            <i class="ti ti-arrow-left me-1"></i>Back to Reservations
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('reservations.store') }}" id="reservationForm">
                        @csrf

                        <div class="row gy-4">
                            <!-- Field Selection -->
                            <div class="col-xl-6">
                                <div class="card custom-card shadow-none border">
                                    <div class="card-header">
                                        <div class="card-title">Field Selection</div>
                                    </div>
                                    <div class="card-body">
                                        <div class="row gy-3">
                                            <!-- Field Selection -->
                                            <div class="col-xl-12">
                                                <label for="field_id" class="form-label">Field <span
                                                        class="text-danger">*</span></label>
                                                <select name="field_id" id="field_id" required
                                                    onchange="updateFieldInfo(); loadAvailability();"
                                                    class="form-select @error('field_id') is-invalid @enderror">
                                                    <option value="">Select Field</option>
                                                    @foreach ($fields as $field)
                                                        <option value="{{ $field->id }}"
                                                            data-rate="{{ $field->hourly_rate }}"
                                                            data-capacity="{{ $field->capacity }}"
                                                            data-type="{{ $field->type }}"
                                                            data-opening="{{ $field->opening_time }}"
                                                            data-closing="{{ $field->closing_time }}"
                                                            data-min-hours="{{ $field->min_booking_hours }}"
                                                            data-max-hours="{{ $field->max_booking_hours }}"
                                                            {{ old('field_id', $selectedField?->id) == $field->id ? 'selected' : '' }}>
                                                            {{ $field->name }} -
                                                            ${{ number_format($field->hourly_rate, 2) }}/hr
                                                        </option>
                                                    @endforeach
                                                </select>
                                                @error('field_id')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- Field Information Display -->
                                            <div class="col-xl-12">
                                                <div id="fieldInfo"
                                                    class="alert alert-info {{ $selectedField ? '' : 'd-none' }}">
                                                    <h6 class="fw-semibold">Field Information</h6>
                                                    <p class="mb-1"><strong>Type:</strong> <span
                                                            id="fieldType">{{ $selectedField?->type }}</span></p>
                                                    <p class="mb-1"><strong>Capacity:</strong> <span
                                                            id="fieldCapacity">{{ $selectedField?->capacity }}</span>
                                                        people</p>
                                                    <p class="mb-1"><strong>Hourly Rate:</strong> $<span
                                                            id="fieldRate">{{ $selectedField ? number_format($selectedField->hourly_rate, 2) : '0.00' }}</span>
                                                    </p>
                                                    <p class="mb-1"><strong>Working Hours:</strong> <span
                                                            id="fieldHours">{{ $selectedField?->opening_time }} -
                                                            {{ $selectedField?->closing_time }}</span></p>
                                                    <p class="mb-0"><strong>Booking Duration:</strong> <span
                                                            id="fieldDuration">{{ $selectedField?->min_booking_hours }} -
                                                            {{ $selectedField?->max_booking_hours }}</span></p>
                                                </div>
                                            </div>

                                            <!-- Date Selection -->
                                            <div class="col-xl-12">
                                                <label for="booking_date" class="form-label">Date <span
                                                        class="text-danger">*</span></label>
                                                <input type="date" name="booking_date" id="booking_date"
                                                    value="{{ old('booking_date', $selectedDate) }}"
                                                    min="{{ date('Y-m-d') }}" required onchange="loadAvailability();"
                                                    class="form-control @error('booking_date') is-invalid @enderror">
                                                @error('booking_date')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- Time Selection -->
                                            <div class="col-xl-12">
                                                <label for="start_time" class="form-label">Time <span
                                                        class="text-danger">*</span></label>
                                                <select name="start_time" id="start_time" required
                                                    onchange="calculateCost();"
                                                    class="form-select @error('start_time') is-invalid @enderror">
                                                    <option value="">Select Time</option>
                                                    <!-- Options will be populated by JavaScript -->
                                                </select>
                                                @error('start_time')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- Duration -->
                                            <div class="col-xl-12">
                                                <label for="duration_hours" class="form-label">Duration <span
                                                        class="text-danger">*</span></label>
                                                <select name="duration_hours" id="duration_hours" required
                                                    onchange="calculateCost(); loadAvailability();"
                                                    class="form-select @error('duration_hours') is-invalid @enderror">
                                                    @for ($i = 1; $i <= 8; $i++)
                                                        <option value="{{ $i }}"
                                                            {{ old('duration_hours', $selectedDuration ?? 1) == $i ? 'selected' : '' }}>
                                                            {{ $i }} {{ Str::plural('hour', $i) }}
                                                        </option>
                                                    @endfor
                                                </select>
                                                @error('duration_hours')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- Utilities -->
                                            <div class="col-xl-12">
                                                <label class="form-label">Add Utility</label>
                                                <div class="row g-2 align-items-center mb-3">
                                                    <div class="col-md-5">
                                                        <select id="utilitySelect" class="form-select">
                                                            <option value="">-- Select Utility --</option>
                                                            @foreach ($utilities as $utility)
                                                                <option value="{{ $utility->id }}"
                                                                    data-name="{{ $utility->name }}"
                                                                    data-rate="{{ $utility->hourly_rate }}">
                                                                    {{ $utility->name }} -
                                                                    XCG {{ number_format($utility->hourly_rate, 2) }}/hr
                                                                </option>
                                                            @endforeach
                                                        </select>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <input type="number" id="utilityQuantity" class="form-control"
                                                            min="1" value="1" placeholder="Hours">
                                                    </div>
                                                    <div class="col-md-2">
                                                        <button type="button" class="btn btn-primary w-100"
                                                            onclick="addUtility()">Add</button>
                                                    </div>
                                                </div>

                                                <table class="table table-bordered" id="utilityTable">
                                                    <thead>
                                                        <tr>
                                                            <th>Utility</th>
                                                            <th>Quantity</th>
                                                            <th>Rate</th>
                                                            <th>Cost</th>
                                                            <th>Action</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <!-- Dynamic rows will be added here -->
                                                    </tbody>
                                                </table>
                                            </div>
                                            <!-- end Utlilities -->

                                            <!-- Availability Check -->
                                            <div class="col-xl-12">
                                                <div id="availabilityCheck" class="d-none">
                                                    <div id="availabilityMessage" class="alert"></div>
                                                </div>
                                            </div>

                                            <!-- Cost Display -->
                                            <div class="col-xl-12">
                                                <div id="costDisplay" class="alert alert-success d-none">
                                                    <h6 class="fw-semibold">Reservation Cost</h6>
                                                    <p class="mb-1"><strong>Total Cost: XCG <span
                                                                id="totalCost">0.00</span></strong></p>
                                                    <p class="mb-0 fs-12">Rate: XCG <span
                                                            id="displayRate">0.00</span>/hour ×
                                                        <span id="displayDuration">1</span> hour(s)
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Customer Information -->
                            <div class="col-xl-6">
                                <div class="card custom-card shadow-none border">
                                    <div class="card-header">
                                        <div class="card-title">Customer Information</div>
                                    </div>
                                    <div class="card-body">
                                        <div class="alert alert-info">
                                            <strong>Note:</strong> Leave customer fields empty to use your account
                                            information.
                                        </div>

                                        <div class="row gy-3">
                                            <!-- Customer Name -->
                                            <div class="col-xl-12">
                                                <label for="customer_name" class="form-label">Customer Name</label>
                                                <input type="text" name="customer_name" id="customer_name"
                                                    value="{{ old('customer_name') }}"
                                                    placeholder="Leave empty to use your name ({{ auth()->user()->name }})"
                                                    class="form-control @error('customer_name') is-invalid @enderror">
                                                @error('customer_name')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- Customer Email -->
                                            <div class="col-xl-12">
                                                <label for="customer_email" class="form-label">Customer Email</label>
                                                <input type="email" name="customer_email" id="customer_email"
                                                    value="{{ old('customer_email') }}"
                                                    placeholder="Leave empty to use your email ({{ auth()->user()->email }})"
                                                    class="form-control @error('customer_email') is-invalid @enderror">
                                                @error('customer_email')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- Customer Phone -->
                                            <div class="col-xl-12">
                                                <label for="customer_phone" class="form-label">Customer Phone</label>
                                                <input type="tel" name="customer_phone" id="customer_phone"
                                                    value="{{ old('customer_phone') }}"
                                                    placeholder="Phone number (optional)"
                                                    class="form-control @error('customer_phone') is-invalid @enderror">
                                                @error('customer_phone')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- Special Requests -->
                                            <div class="col-xl-12">
                                                <label for="special_requests" class="form-label">Special Requests</label>
                                                <textarea name="special_requests" id="special_requests" rows="3"
                                                    class="form-control @error('special_requests') is-invalid @enderror"
                                                    placeholder="Any special requirements or notes...">{{ old('special_requests') }}</textarea>
                                                @error('special_requests')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- Reservation Rules -->
                                            <div class="col-xl-12">
                                                <div class="alert alert-warning">
                                                    <h6 class="fw-semibold">FPMP Reservation Rules</h6>
                                                    <ul class="mb-0 fs-12">
                                                        <li> Reservations are available from 8:00 AM to 10:00 PM</li>
                                                        <li> All reservations are automatically confirmed</li>
                                                        <li> Cancellations must be made at least 24 hours in advance</li>
                                                        <li> Modifications must be made at least 24 hours in advance</li>
                                                        <li> Please arrive 15 minutes before your scheduled time</li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="row">
                            <div class="col-xl-12">
                                <div class="d-flex gap-2 justify-content-end mt-4 pt-3 border-top">
                                    <a href="{{ route('reservations.index') }}" class="btn btn-secondary">
                                        <i class="ti ti-x me-1"></i>Cancel
                                    </a>
                                    <button type="submit" id="submitBtn" class="btn btn-primary">
                                        <i class="ti ti-check me-1"></i>Create Reservation
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        function updateFieldInfo() {
            const fieldSelect = document.getElementById('field_id');
            const selectedOption = fieldSelect.options[fieldSelect.selectedIndex];
            const fieldInfo = document.getElementById('fieldInfo');

            if (selectedOption.value) {
                document.getElementById('fieldType').textContent = selectedOption.dataset.type || 'N/A';
                document.getElementById('fieldCapacity').textContent = selectedOption.dataset.capacity || 'N/A';
                document.getElementById('fieldRate').textContent = parseFloat(selectedOption.dataset.rate || 0).toFixed(2);
                document.getElementById('fieldHours').textContent = (selectedOption.dataset.opening || '08:00') + ' - ' + (
                    selectedOption.dataset.closing || '22:00');
                document.getElementById('fieldDuration').textContent = (selectedOption.dataset.minHours || '1') + ' - ' + (
                    selectedOption.dataset.maxHours || '8') + ' hours';
                fieldInfo.classList.remove('d-none');
                updateDurationOptions();
                calculateCost();
            } else {
                fieldInfo.classList.add('d-none');
                document.getElementById('costDisplay').classList.add('d-none');
            }
        }

        function updateDurationOptions() {
            const fieldSelect = document.getElementById('field_id');
            const selectedOption = fieldSelect.options[fieldSelect.selectedIndex];
            const durationSelect = document.getElementById('duration_hours');

            if (selectedOption.value) {
                const minHours = parseInt(selectedOption.dataset.minHours || 1);
                const maxHours = parseInt(selectedOption.dataset.maxHours || 8);

                // Clear existing options
                durationSelect.innerHTML = '';

                // Add new options based on field constraints
                for (let i = minHours; i <= maxHours; i++) {
                    const option = document.createElement('option');
                    option.value = i;
                    option.textContent = i + ' ' + (i === 1 ? 'hour' : 'hours');
                    if (i === 1) option.selected = true;
                    durationSelect.appendChild(option);
                }
            }
        }

        function calculateCost() {
            const fieldSelect = document.getElementById('field_id');
            const duration = document.getElementById('duration_hours').value;
            const selectedOption = fieldSelect.options[fieldSelect.selectedIndex];

            if (selectedOption.value && duration) {
                const rate = parseFloat(selectedOption.dataset.rate || 0);
                const total = rate * parseInt(duration);

                document.getElementById('totalCost').textContent = total.toFixed(2);
                document.getElementById('displayRate').textContent = rate.toFixed(2);
                document.getElementById('displayDuration').textContent = duration;
                document.getElementById('costDisplay').classList.remove('d-none');
            } else {
                document.getElementById('costDisplay').classList.add('d-none');
            }
        }

        function loadAvailability() {
            const fieldId = document.getElementById('field_id').value;
            const date = document.getElementById('booking_date').value;
            const duration = document.getElementById('duration_hours').value;

            if (!fieldId || !date || !duration) {
                return;
            }

            // Load available time slots
            fetch(`{{ route('reservations.check-availability') }}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        field_id: fieldId,
                        date: date,
                        duration_hours: parseInt(duration)
                    })
                })
                .then(response => response.json())
                .then(data => {
                    updateTimeSlots(data.slots || []);
                })
                .catch(error => {
                    console.error('Error loading availability:', error);
                });
        }

        function updateTimeSlots(slots) {
            const timeSelect = document.getElementById('start_time');
            const currentValue = timeSelect.value || '{{ $selectedTime ?? '' }}';

            // Clear existing options
            timeSelect.innerHTML = '<option value="">Select Time</option>';

            // Add available slots
            slots.forEach(slot => {
                const option = document.createElement('option');
                option.value = slot.start_time;
                option.textContent = slot.display;
                if (slot.start_time === currentValue) {
                    option.selected = true;
                }
                timeSelect.appendChild(option);
            });
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            updateFieldInfo();
            calculateCost();
            loadAvailability();
        });
    </script>

    <script>
        /////////////////////////////////// di utilities
        let utilities = [];

        function addUtility() {
            const select = document.getElementById('utilitySelect');
            const quantityInput = document.getElementById('utilityQuantity');
            const tableBody = document.querySelector('#utilityTable tbody');

            const id = select.value;
            const name = select.options[select.selectedIndex].dataset.name;
            const rate = parseFloat(select.options[select.selectedIndex].dataset.rate || 0);
            const quantity = parseInt(quantityInput.value || 1);

            if (!id || quantity <= 0) {
                alert("Please select a utility and enter valid hours.");
                return;
            }

            if (utilities.find(u => u.id === id)) {
                alert("This utility is already added.");
                return;
            }

            const cost = rate * quantity;

            utilities.push({
                id,
                name,
                rate,
                quantity
            });

            const row = document.createElement('tr');
            row.innerHTML = `
        <td>${name}</td>
        <td>${quantity}</td>
        <td>$${rate.toFixed(2)}</td>
        <td>$${cost.toFixed(2)}</td>
        <td>
            <button type="button" class="btn btn-danger btn-sm" onclick="removeUtility('${id}', this)">Remove</button>
        </td>
        <input type="hidden" name="utilities[${id}][id]" value="${id}">
        <input type="hidden" name="utilities[${id}][hours]" value="${quantity}">
    `;

            tableBody.appendChild(row);

            // Reset
            select.selectedIndex = 0;
            quantityInput.value = 1;

            calculateCost();
        }

        function removeUtility(id, btn) {
            utilities = utilities.filter(u => u.id !== id);
            btn.closest('tr').remove();
            calculateCost();
        }

        function calculateCost() {
            const fieldSelect = document.getElementById('field_id');
            const duration = parseInt(document.getElementById('duration_hours').value || 1);
            const rate = parseFloat(fieldSelect.options[fieldSelect.selectedIndex]?.dataset.rate || 0);
            let total = rate * duration;

            utilities.forEach(u => {
                total += u.rate * u.quantity;
            });

            document.getElementById('totalCost').textContent = total.toFixed(2);
            document.getElementById('displayRate').textContent = rate.toFixed(2);
            document.getElementById('displayDuration').textContent = duration;
            document.getElementById('costDisplay').classList.remove('d-none');
        }
    </script>
@endpush
