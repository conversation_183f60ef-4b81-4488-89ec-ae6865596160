@extends('layouts.admin')

@section('title', 'Edit Reservation - FPMP Sport Park')

@section('content')
    <!-- <PERSON> Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <h1 class="page-title fw-semibold fs-18 mb-0">Edit Reservation #{{ $reservation->id }}</h1>
        <div class="ms-md-1 ms-0">
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('reservations.index') }}">Reservations</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('reservations.show', $reservation) }}">Reservation
                            #{{ $reservation->id }}</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Edit</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- <PERSON> Header Close -->

    <!-- Success/Error Messages -->
    @if (session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="ti ti-check-circle me-2"></i>{{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if (session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="ti ti-exclamation-triangle me-2"></i>{{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <!-- Edit Reservation Form -->
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header justify-content-between">
                    <div class="card-title">
                        <i class="ti ti-edit me-2"></i>Edit Reservation
                        <span
                            class="badge bg-{{ $reservation->status_color }}-transparent text-{{ $reservation->status_color }} ms-2">
                            {{ $reservation->status }}
                        </span>
                    </div>
                    <div class="d-flex gap-2">
                        <a href="{{ route('reservations.show', $reservation) }}" class="btn btn-secondary btn-sm">
                            <i class="ti ti-arrow-left me-1"></i>Back to Details
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('reservations.update', $reservation) }}" id="reservationForm">
                        @csrf
                        @method('PUT')

                        <div class="row gy-4">
                            <!-- Field Selection -->
                            <div class="col-xl-6">
                                <div class="card custom-card shadow-none border">
                                    <div class="card-header">
                                        <div class="card-title">Field & Schedule</div>
                                    </div>
                                    <div class="card-body">
                                        <div class="row gy-3">
                                            <!-- Field Selection -->
                                            <div class="col-xl-12">
                                                <label for="field_id" class="form-label">Field <span
                                                        class="text-danger">*</span></label>
                                                <select name="field_id" id="field_id" required
                                                    onchange="updateFieldInfo(); loadAvailability();"
                                                    class="form-select @error('field_id') is-invalid @enderror">
                                                    <option value="">Select Field</option>
                                                    @foreach ($fields as $field)
                                                        <option value="{{ $field->id }}"
                                                            data-rate="{{ $field->hourly_rate }}"
                                                            data-capacity="{{ $field->capacity }}"
                                                            data-type="{{ $field->type }}"
                                                            data-opening="{{ $field->opening_time }}"
                                                            data-closing="{{ $field->closing_time }}"
                                                            data-min-hours="{{ $field->min_booking_hours }}"
                                                            data-max-hours="{{ $field->max_booking_hours }}"
                                                            {{ old('field_id', $reservation->field_id) == $field->id ? 'selected' : '' }}>
                                                            {{ $field->name }} -
                                                            XCG {{ number_format($field->hourly_rate, 2) }}/hr
                                                        </option>
                                                    @endforeach
                                                </select>
                                                @error('field_id')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- Field Information Display -->
                                            <div class="col-xl-12">
                                                <div id="fieldInfo" class="alert alert-info">
                                                    <h6 class="fw-semibold">Field Information</h6>
                                                    <p class="mb-1"><strong>Type:</strong> <span
                                                            id="fieldType">{{ $reservation->field->type }}</span></p>
                                                    <p class="mb-1"><strong>Capacity:</strong> <span
                                                            id="fieldCapacity">{{ $reservation->field->capacity }}</span>
                                                        people</p>
                                                    <p class="mb-1"><strong>Hourly Rate:</strong> XCG <span
                                                            id="fieldRate">{{ number_format($reservation->field->hourly_rate, 2) }}</span>
                                                    </p>
                                                    <p class="mb-1"><strong>Working Hours:</strong> <span
                                                            id="fieldHours">{{ $reservation->field->opening_time }} -
                                                            {{ $reservation->field->closing_time }}</span></p>
                                                    <p class="mb-0"><strong>Booking Duration:</strong> <span
                                                            id="fieldDuration">{{ $reservation->field->min_booking_hours }}
                                                            - {{ $reservation->field->max_booking_hours }}</span> hours</p>
                                                </div>
                                            </div>

                                            <!-- Date Selection -->
                                            <div class="col-xl-12">
                                                <label for="booking_date" class="form-label">Date <span
                                                        class="text-danger">*</span></label>
                                                <input type="date" name="booking_date" id="booking_date"
                                                    value="{{ old('booking_date', $reservation->booking_date->format('Y-m-d')) }}"
                                                    min="{{ date('Y-m-d') }}" required onchange="loadAvailability();"
                                                    class="form-control @error('booking_date') is-invalid @enderror">
                                                @error('booking_date')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- Time Selection -->
                                            <div class="col-xl-12">
                                                <label for="start_time" class="form-label">Time <span
                                                        class="text-danger">*</span></label>
                                                <select name="start_time" id="start_time" required
                                                    onchange="calculateCost();"
                                                    class="form-select @error('start_time') is-invalid @enderror">
                                                    <option value="">Select Time</option>
                                                    <!-- Options will be populated by JavaScript -->
                                                </select>
                                                @error('start_time')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- Duration -->
                                            <div class="col-xl-12">
                                                <label for="duration_hours" class="form-label">Duration <span
                                                        class="text-danger">*</span></label>
                                                <select name="duration_hours" id="duration_hours" required
                                                    onchange="calculateCost(); loadAvailability();"
                                                    class="form-select @error('duration_hours') is-invalid @enderror">
                                                    @for ($i = 1; $i <= 8; $i++)
                                                        <option value="{{ $i }}"
                                                            {{ old('duration_hours', $reservation->duration_hours) == $i ? 'selected' : '' }}>
                                                            {{ $i }} {{ Str::plural('hour', $i) }}
                                                        </option>
                                                    @endfor
                                                </select>
                                                @error('duration_hours')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- Utilities -->
                                            <div class="col-xl-12">
                                                <label class="form-label">Add Utility</label>
                                                <div class="row g-2 align-items-center mb-3">
                                                    <div class="col-md-5">
                                                        <select id="utilitySelect" class="form-select">
                                                            <option value="">-- Select Utility --</option>
                                                            @foreach ($utilities as $utility)
                                                                <option value="{{ $utility->id }}"
                                                                    data-name="{{ $utility->name }}"
                                                                    data-rate="{{ $utility->hourly_rate }}">
                                                                    {{ $utility->name }} -
                                                                    ${{ number_format($utility->hourly_rate, 2) }}/hr
                                                                </option>
                                                            @endforeach
                                                        </select>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <input type="number" id="utilityQuantity" class="form-control"
                                                            min="1" value="1" placeholder="Hours">
                                                    </div>
                                                    <div class="col-md-2">
                                                        <button type="button" class="btn btn-primary w-100"
                                                            onclick="addUtility()">Add</button>
                                                    </div>
                                                </div>

                                                <table class="table table-bordered" id="utilityTable">
                                                    <thead>
                                                        <tr>
                                                            <th>Utility</th>
                                                            <th>Hours</th>
                                                            <th>Rate</th>
                                                            <th>Cost</th>
                                                            <th>Action</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <!-- Existing utilities will be populated by JS -->
                                                    </tbody>
                                                </table>
                                            </div>
                                            <!-- end Utilities -->

                                            <!-- Availability Check -->
                                            <div class="col-xl-12">
                                                <div id="availabilityCheck" class="d-none">
                                                    <div id="availabilityMessage" class="alert"></div>
                                                </div>
                                            </div>
                                            <!------------------------------------------------------------------>
                                            <!-- Cost Display -->
                                            <div class="col-xl-12">
                                                <div id="costDisplay" class="alert alert-success d-none">
                                                    <h6 class="fw-semibold">Reservation Cost</h6>
                                                    <p class="mb-1"><strong>Total Cost: XCG <span
                                                                id="totalCost">0.00</span></strong></p>
                                                    <p class="mb-0 fs-12">Rate: XCG <span
                                                            id="displayRate">0.00</span>/hour ×
                                                        <span id="displayDuration">1</span> hour(s)
                                                    </p>
                                                </div>
                                            </div>
                                            <!------------------------------------------------------------------>
                                            <!-- Cost Display -->
                                            <!--<div class="col-xl-12">
                                                                            <div id="costDisplay" class="alert alert-success">
                                                                                <h6 class="fw-semibold">Reservation Cost</h6>
                                                                                <p class="mb-1">
                                                                                    <strong>Total Cost: $<span id="totalCost">
                                                                                            {{ number_format($reservation->total_cost + $reservationUtilities->sum('cost'), 2) }}
                                                                                        </span></strong>
                                                                                </p>
                                                                                <p class="mb-0 fs-12">
                                                                                    Field: $<span
                                                                                        id="displayRate">{{ number_format($reservation->field->hourly_rate, 2) }}</span>/hour
                                                                                    ×
                                                                                    <span
                                                                                        id="displayDuration">{{ $reservation->duration_hours }}</span>
                                                                                    hour(s)
                                                                                    = $<span
                                                                                        id="fieldCost">{{ number_format($reservation->total_cost, 2) }}</span>
                                                                                </p>
                                                                                <p class="mb-0 fs-12">
                                                                                    Utilities: $<span id="utilitiesCost">
                                                                                        {{ number_format($reservation->total_cost + $reservationUtilities->sum('cost'), 2) }}
                                                                                    </span>
                                                                                </p>
                                                                            </div>
                                                                        </div>-->
                                            <!--<div class="col-xl-12">
                                                                                                                                        <div id="costDisplay" class="alert alert-success">
                                                                                                                                            <h6 class="fw-semibold">Reservation Cost</h6>
                                                                                                                                            <p class="mb-1"><strong>Total Cost: $<span
                                                                                                                                                        id="totalCost">{{ number_format($reservation->total_cost, 2) }}</span></strong>
                                                                                                                                            </p>
                                                                                                                                            <p class="mb-0 fs-12">Rate: $<span
                                                                                                                                                    id="displayRate">{{ number_format($reservation->field->hourly_rate, 2) }}</span>/hour
                                                                                                                                                × <span
                                                                                                                                                    id="displayDuration">{{ $reservation->duration_hours }}</span>
                                                                                                                                                hour(s)</p>
                                                                                                                                        </div>
                                                                                                                                    </div>-->
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Customer Information -->
                            <div class="col-xl-6">
                                <div class="card custom-card shadow-none border">
                                    <div class="card-header">
                                        <div class="card-title">Customer Information</div>
                                    </div>
                                    <div class="card-body">
                                        <div class="alert alert-info">
                                            <strong>Note:</strong> Leave customer fields empty to use your account
                                            information.
                                        </div>

                                        <div class="row gy-3">
                                            <!-- Customer Name -->
                                            <div class="col-xl-12">
                                                <label for="customer_name" class="form-label">Customer Name</label>
                                                <input type="text" name="customer_name" id="customer_name"
                                                    value="{{ old('customer_name', $reservation->customer_name) }}"
                                                    placeholder="Leave empty to use your name ({{ auth()->user()->name }})"
                                                    class="form-control @error('customer_name') is-invalid @enderror">
                                                @error('customer_name')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- Customer Email -->
                                            <div class="col-xl-12">
                                                <label for="customer_email" class="form-label">Customer Email</label>
                                                <input type="email" name="customer_email" id="customer_email"
                                                    value="{{ old('customer_email', $reservation->customer_email) }}"
                                                    placeholder="Leave empty to use your email ({{ auth()->user()->email }})"
                                                    class="form-control @error('customer_email') is-invalid @enderror">
                                                @error('customer_email')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- Customer Phone -->
                                            <div class="col-xl-12">
                                                <label for="customer_phone" class="form-label">Customer Phone</label>
                                                <input type="tel" name="customer_phone" id="customer_phone"
                                                    value="{{ old('customer_phone', $reservation->customer_phone) }}"
                                                    placeholder="Phone number (optional)"
                                                    class="form-control @error('customer_phone') is-invalid @enderror">
                                                @error('customer_phone')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- Special Requests -->
                                            <div class="col-xl-12">
                                                <label for="special_requests" class="form-label">Special Requests</label>
                                                <textarea name="special_requests" id="special_requests" rows="3"
                                                    class="form-control @error('special_requests') is-invalid @enderror"
                                                    placeholder="Any special requirements or notes...">{{ old('special_requests', $reservation->special_requests) }}</textarea>
                                                @error('special_requests')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- Current Reservation Info -->
                                            <div class="col-xl-12">
                                                <div class="alert alert-warning">
                                                    <h6 class="fw-semibold">Current Reservation</h6>
                                                    <ul class="mb-0 fs-12">
                                                        <li><strong>Original Date:</strong>
                                                            {{ $reservation->booking_date->format('M d, Y') }}</li>
                                                        <li><strong>Original Time:</strong> {{ $reservation->time_range }}
                                                        </li>
                                                        <li><strong>Original Cost:</strong>
                                                            XCG {{ number_format($reservation->total_cost, 2) }}</li>
                                                        <li><strong>Status:</strong> {{ $reservation->status }}</li>
                                                    </ul>
                                                </div>
                                            </div>

                                            <!-- Modification Rules -->
                                            <div class="col-xl-12">
                                                <div class="alert alert-info">
                                                    <h6 class="fw-semibold">Modification Rules</h6>
                                                    <ul class="mb-0 fs-12">
                                                        <li> Modifications must be made at least 24 hours in advance</li>
                                                        <li> New time slot must be available</li>
                                                        <li> Cost will be recalculated based on new selection</li>
                                                        <li> Reservation will remain confirmed after modification</li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="row">
                            <div class="col-xl-12">
                                <div class="d-flex gap-2 justify-content-end mt-4 pt-3 border-top">
                                    <a href="{{ route('reservations.show', $reservation) }}" class="btn btn-secondary">
                                        <i class="ti ti-x me-1"></i>Cancel
                                    </a>
                                    <button type="submit" id="submitBtn" class="btn btn-primary">
                                        <i class="ti ti-check me-1"></i>Update Reservation
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        // Store original values for comparison
        const originalValues = {
            field_id: '{{ $reservation->field_id }}',
            booking_date: '{{ $reservation->booking_date->format('Y-m-d') }}',
            start_time: '{{ $reservation->start_time }}',
            duration_hours: '{{ $reservation->duration_hours }}'
        };

        function updateFieldInfo() {
            const fieldSelect = document.getElementById('field_id');
            const selectedOption = fieldSelect.options[fieldSelect.selectedIndex];
            const fieldInfo = document.getElementById('fieldInfo');

            if (selectedOption.value) {
                document.getElementById('fieldType').textContent = selectedOption.dataset.type || 'N/A';
                document.getElementById('fieldCapacity').textContent = selectedOption.dataset.capacity || 'N/A';
                document.getElementById('fieldRate').textContent = parseFloat(selectedOption.dataset.rate || 0).toFixed(2);
                document.getElementById('fieldHours').textContent = (selectedOption.dataset.opening || '08:00') + ' - ' + (
                    selectedOption.dataset.closing || '22:00');
                document.getElementById('fieldDuration').textContent = (selectedOption.dataset.minHours || '1') + ' - ' + (
                    selectedOption.dataset.maxHours || '8') + ' hours';
                fieldInfo.classList.remove('d-none');
                updateDurationOptions();
                calculateCost();
            } else {
                fieldInfo.classList.add('d-none');
                document.getElementById('costDisplay').classList.add('d-none');
            }
        }

        function updateDurationOptions() {
            const fieldSelect = document.getElementById('field_id');
            const selectedOption = fieldSelect.options[fieldSelect.selectedIndex];
            const durationSelect = document.getElementById('duration_hours');
            const currentValue = durationSelect.value;

            if (selectedOption.value) {
                const minHours = parseInt(selectedOption.dataset.minHours || 1);
                const maxHours = parseInt(selectedOption.dataset.maxHours || 8);

                // Clear existing options
                durationSelect.innerHTML = '';

                // Add new options based on field constraints
                for (let i = minHours; i <= maxHours; i++) {
                    const option = document.createElement('option');
                    option.value = i;
                    option.textContent = i + ' ' + (i === 1 ? 'hour' : 'hours');
                    if (i == currentValue) option.selected = true;
                    durationSelect.appendChild(option);
                }
            }
        }

        /*function calculateCost() {
            const fieldSelect = document.getElementById('field_id');
            const duration = document.getElementById('duration_hours').value;
            const selectedOption = fieldSelect.options[fieldSelect.selectedIndex];

            if (selectedOption.value && duration) {
                const rate = parseFloat(selectedOption.dataset.rate || 0);
                const total = rate * parseInt(duration);

                document.getElementById('totalCost').textContent = total.toFixed(2);
                document.getElementById('displayRate').textContent = rate.toFixed(2);
                document.getElementById('displayDuration').textContent = duration;
                document.getElementById('costDisplay').classList.remove('d-none');
            } else {
                document.getElementById('costDisplay').classList.add('d-none');
            }
        } */

        /////////////////////////////////////////////////////////////////////////////////////////////////
        function calculateCost() {
            const fieldSelect = document.getElementById('field_id');
            const duration = document.getElementById('duration_hours').value;
            const selectedOption = fieldSelect.options[fieldSelect.selectedIndex];

            let fieldTotal = 0;
            let utilitiesTotal = 0;

            if (selectedOption.value && duration) {
                const rate = parseFloat(selectedOption.dataset.rate || 0);
                fieldTotal = rate * parseInt(duration);

                document.getElementById('displayRate').textContent = rate.toFixed(2);
                document.getElementById('displayDuration').textContent = duration;
                document.getElementById('fieldCost').textContent = fieldTotal.toFixed(2);
            }

            // Loop through utility rows and calculate total
            const utilityRows = document.querySelectorAll('#utilityTable tbody tr');
            utilityRows.forEach(row => {
                const hours = parseFloat(row.querySelector('.utility-hours')?.textContent || 0);
                const rate = parseFloat(row.querySelector('.utility-rate')?.textContent || 0);
                const cost = hours * rate;
                utilitiesTotal += cost;
            });

            document.getElementById('utilitiesCost').textContent = utilitiesTotal.toFixed(2);
            document.getElementById('totalCost').textContent = (fieldTotal + utilitiesTotal).toFixed(2);

            document.getElementById('costDisplay').classList.remove('d-none');
        }
        /////////////////////////////////////////////////////////////////////////////////////////////////

        function loadAvailability() {
            const fieldId = document.getElementById('field_id').value;
            const date = document.getElementById('booking_date').value;
            const duration = document.getElementById('duration_hours').value;

            if (!fieldId || !date || !duration) {
                return;
            }

            // Load available time slots
            fetch(`{{ route('reservations.check-availability') }}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        field_id: fieldId,
                        date: date,
                        duration_hours: parseInt(duration),
                        exclude_reservation_id: {{ $reservation->id }}
                    })
                })
                .then(response => response.json())
                .then(data => {
                    updateTimeSlots(data.slots || []);
                })
                .catch(error => {
                    console.error('Error loading availability:', error);
                });
        }

        function updateTimeSlots(slots) {
            const timeSelect = document.getElementById('start_time');
            const currentValue = timeSelect.value || originalValues.start_time;

            // Clear existing options
            timeSelect.innerHTML = '<option value="">Select Time</option>';

            // Add available slots
            slots.forEach(slot => {
                const option = document.createElement('option');
                option.value = slot.start_time;
                option.textContent = slot.display;
                if (slot.start_time === currentValue) {
                    option.selected = true;
                }
                timeSelect.appendChild(option);
            });

            // If original time is not available, show warning
            if (currentValue && !slots.find(slot => slot.start_time === currentValue)) {
                const availabilityCheck = document.getElementById('availabilityCheck');
                const availabilityMessage = document.getElementById('availabilityMessage');
                availabilityMessage.className = 'alert alert-warning';
                availabilityMessage.textContent = 'Original time slot is no longer available. Please select a new time.';
                availabilityCheck.classList.remove('d-none');
            } else {
                document.getElementById('availabilityCheck').classList.add('d-none');
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            updateFieldInfo();
            calculateCost();
            loadAvailability();

            if (Array.isArray(existingUtilities)) {
                existingUtilities.forEach(util => {
                    addUtility(util.id, util.name, parseFloat(util.rate), parseInt(util.hours));
                });
            }
        });
    </script>


    <script>
        /////////////////////////////////// di utilities
        let utilities = [];

        function addUtility(id = null, name = '', rate = 0, quantity = 1) {
            const tableBody = document.querySelector('#utilityTable tbody');

            if (!id) {
                const select = document.getElementById('utilitySelect');
                const quantityInput = document.getElementById('utilityQuantity');

                id = select.value;
                name = select.options[select.selectedIndex].dataset.name;
                rate = parseFloat(select.options[select.selectedIndex].dataset.rate || 0);
                quantity = parseInt(quantityInput.value || 1);

                if (!id || quantity <= 0) {
                    alert("Please select a utility and enter valid hours.");
                    return;
                }

                if (utilities.find(u => u.id === id)) {
                    alert("This utility is already added.");
                    return;
                }
            }

            if (utilities.find(u => u.id === id)) return;

            const cost = rate * quantity;

            utilities.push({
                id,
                name,
                rate,
                quantity
            });

            const row = document.createElement('tr');
            row.innerHTML = `
        <td>${name}</td>
        <td class="utility-hours">${quantity}</td>
        <td class="utility-rate">${rate.toFixed(2)}</td>
        <td>XCG ${cost.toFixed(2)}</td>
        <td>
            <button type="button" class="btn btn-danger btn-sm" onclick="removeUtility('${id}', this)">Remove</button>
        </td>
        <input type="hidden" name="utilities[${id}][id]" value="${id}">
        <input type="hidden" name="utilities[${id}][hours]" value="${quantity}">
    `;

            tableBody.appendChild(row);
            calculateCost();
        }



        function removeUtility(id, btn) {
            utilities = utilities.filter(u => u.id !== id);
            btn.closest('tr').remove();
            calculateCost();
        }

        function calculateCost() {
            const fieldSelect = document.getElementById('field_id');
            const duration = parseInt(document.getElementById('duration_hours').value || 1);
            const rate = parseFloat(fieldSelect.options[fieldSelect.selectedIndex]?.dataset.rate || 0);
            let total = rate * duration;

            utilities.forEach(u => {
                total += u.rate * u.quantity;
            });

            document.getElementById('totalCost').textContent = total.toFixed(2);
            document.getElementById('displayRate').textContent = rate.toFixed(2);
            document.getElementById('displayDuration').textContent = duration;
            document.getElementById('costDisplay').classList.remove('d-none');
        }
    </script>

    <script>
        const existingUtilities = @json($reservationUtilities);
    </script>
@endpush
