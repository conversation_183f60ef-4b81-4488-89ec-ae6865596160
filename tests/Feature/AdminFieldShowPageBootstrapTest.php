<?php

namespace Tests\Feature;

use App\Models\Amenity;
use App\Models\Booking;
use App\Models\Field;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(\App\Http\Controllers\Admin\FieldController::class)]
class AdminFieldShowPageBootstrapTest extends TestCase
{
    use RefreshDatabase;

    protected $admin;

    protected $field;

    protected function setUp(): void
    {
        parent::setUp();

        // Create admin user
        $this->admin = User::factory()->create(['role' => 'admin']);

        // Create a test field with comprehensive data
        $this->field = Field::factory()->create([
            'name' => 'Bootstrap Test Field',
            'type' => 'Soccer',
            'description' => 'Professional soccer field with Bootstrap styling',
            'hourly_rate' => 85.00,
            'capacity' => 24,
            'status' => 'Active',
            'opening_time' => '08:00',
            'closing_time' => '22:00',
        ]);

        // Create amenities and attach them to the field
        $amenities = [
            ['name' => 'Lighting', 'icon_class' => 'ri-lightbulb-line'],
            ['name' => 'Parking', 'icon_class' => 'ri-car-line'],
            ['name' => 'Restrooms', 'icon_class' => 'ri-home-line'],
        ];

        foreach ($amenities as $amenityData) {
            $amenity = \App\Models\Amenity::factory()->create($amenityData);
            $this->field->amenities()->attach($amenity->id);
        }

        // Create a test booking for the field
        $customer = User::factory()->create(['role' => 'user']);
        Booking::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $customer->id,
            'booking_date' => now()->addDays(1),
            'start_time' => '10:00',
            'end_time' => '12:00',
            'duration_hours' => 2,
            'status' => 'Confirmed',
            'total_cost' => 170.00,
        ]);
    }

    #[Test]
    public function admin_field_show_page_loads_with_bootstrap_styling()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('admin.fields.show', $this->field));

        $response->assertStatus(200);

        // Check for Bootstrap classes instead of Tailwind
        $response->assertSee('card custom-card');
        $response->assertSee('card-header');
        $response->assertSee('card-body');
        $response->assertSee('btn btn-sm');
        $response->assertSee('badge bg-');

        // Should not see Tailwind classes
        $response->assertDontSee('bg-gray-50');
        $response->assertDontSee('text-gray-900');
        $response->assertDontSee('px-6 py-3');

        echo "✅ Bootstrap styling is properly implemented\n";
    }

    #[Test]
    public function field_show_page_displays_all_content_correctly()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('admin.fields.show', $this->field));

        $response->assertStatus(200);

        // Basic Information
        $response->assertSee($this->field->name);
        $response->assertSee($this->field->type);
        $response->assertSee($this->field->description);
        $response->assertSee($this->field->status);

        // Pricing and Capacity
        $response->assertSee('XCG '.number_format($this->field->hourly_rate, 2));
        $response->assertSee($this->field->capacity.' people');

        // Timestamps
        $response->assertSee($this->field->created_at->format('M d, Y H:i'));
        $response->assertSee($this->field->updated_at->format('M d, Y H:i'));

        echo "✅ All field content displays correctly\n";
    }

    #[Test]
    public function field_show_page_displays_amenities_with_bootstrap()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('admin.fields.show', $this->field));

        $response->assertStatus(200);

        // Check amenities section (feature removed)
        $response->assertDontSee('Available Amenities');

        // Reload the field with amenities to ensure they're loaded
        $this->field->load('amenities');

        // Check specific amenities
        foreach ($this->field->amenities as $amenity) {
            $response->assertSee($amenity->name);
            // Check for the icon class - either the amenity's icon or the default
            $iconClass = $amenity->icon_class ?? 'ri-checkbox-circle-line';
            $response->assertSee($iconClass);
        }

        echo "✅ Amenities display correctly with Bootstrap styling\n";
    }

    #[Test]
    public function field_show_page_displays_bookings_table()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('admin.fields.show', $this->field));

        $response->assertStatus(200);

        // Check for Bootstrap table
        $response->assertSee('table table-striped');
        $response->assertSee('table-responsive');
        $response->assertSee('Recent Bookings');

        // Check booking data
        $response->assertSee('Confirmed');
        $response->assertSee('XCG 170.00');

        echo "✅ Bookings table displays correctly\n";
    }

    #[Test]
    public function navigation_buttons_work_with_bootstrap_styling()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('admin.fields.show', $this->field));

        $response->assertStatus(200);

        // Check for Bootstrap button classes
        $response->assertSee('btn btn-sm btn-primary');
        $response->assertSee('btn btn-sm btn-danger');
        $response->assertSee('btn btn-sm btn-secondary');

        // Check for Remix icons
        $response->assertSee('ri-edit-line');
        $response->assertSee('ri-delete-bin-line');
        $response->assertSee('ri-arrow-left-line');

        // Check button functionality
        $response->assertSee('Edit Field');
        $response->assertSee('Delete Field');
        $response->assertSee('Back to Fields');

        echo "✅ Navigation buttons have proper Bootstrap styling\n";
    }

    #[Test]
    public function delete_modal_uses_bootstrap_modal()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('admin.fields.show', $this->field));

        $response->assertStatus(200);

        // Check for Bootstrap modal classes
        $response->assertSee('modal fade');
        $response->assertSee('modal-dialog');
        $response->assertSee('modal-content');
        $response->assertSee('modal-header');
        $response->assertSee('modal-body');
        $response->assertSee('modal-footer');

        // Check modal attributes
        $response->assertSee('data-bs-toggle');
        $response->assertSee('data-bs-target');
        $response->assertSee('data-bs-dismiss');
        $response->assertSee('#deleteModal');

        // Should not see old Tailwind modal classes
        $response->assertDontSee('fixed inset-0');
        $response->assertDontSee('bg-gray-600 bg-opacity-50');

        echo "✅ Delete modal uses proper Bootstrap modal\n";
    }

    #[Test]
    public function field_show_page_has_proper_admin_layout()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('admin.fields.show', $this->field));

        $response->assertStatus(200);

        // Check for admin layout elements
        $response->assertSee('Field Details');
        $response->assertSee('breadcrumb');
        $response->assertSee('page-header-breadcrumb');

        // Check breadcrumb navigation
        $response->assertSee('Home');
        $response->assertSee('Fields');
        $response->assertSee('Details');

        // Should not see layout errors
        $response->assertDontSee('View [layouts.app] not found');
        $response->assertDontSee('x-app-layout');

        echo "✅ Admin layout is properly implemented\n";
    }
}
