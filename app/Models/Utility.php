<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Utility extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'description',
        'icon_class',
        'hourly_rate',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
        'hourly_rate' => 'decimal:2',
    ];

    /**
     * Get the fields that have this utility.
     */
    public function fields(): BelongsToMany
    {
        return $this->belongsToMany(Field::class, 'field_utility')
            ->withTimestamps();
    }

    /**
     * Scope a query to only include active utilities.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include inactive utilities.
     */
    public function scopeInactive($query)
    {
        return $query->where('is_active', false);
    }

    /**
     * Get the utility's display name with status.
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->name.($this->is_active ? '' : ' (Inactive)');
    }

    /**
     * Get the utility's status badge class.
     */
    public function getStatusBadgeClassAttribute(): string
    {
        return $this->is_active ? 'bg-success' : 'bg-danger';
    }

    /**
     * Get the utility's status text.
     */
    public function getStatusTextAttribute(): string
    {
        return $this->is_active ? 'Active' : 'Inactive';
    }

    /**
     * Get the formatted hourly rate.
     */
    public function getFormattedHourlyRateAttribute(): string
    {
        if ($this->hourly_rate === null) {
            return 'N/A';
        }

        return 'XCG '.number_format($this->hourly_rate, 2);
    }

    /**
     * Check if the utility can be deleted.
     */
    public function canBeDeleted(): bool
    {
        return $this->fields()->count() === 0;
    }

    /**
     * Get the count of fields using this utility.
     */
    public function getFieldsCountAttribute(): int
    {
        return $this->fields()->count();
    }

    /**
     * Activate the utility.
     */
    public function activate(): bool
    {
        return $this->update(['is_active' => true]);
    }

    /**
     * Deactivate the utility.
     */
    public function deactivate(): bool
    {
        return $this->update(['is_active' => false]);
    }

    /**
     * Toggle the utility's active status.
     */
    public function toggleStatus(): bool
    {
        return $this->update(['is_active' => ! $this->is_active]);
    }

    // /////////////////////////////////////////////////////////////////////////////////
    public function reservations()
    {
        return $this->belongsToMany(Reservation::class)
            ->withPivot('hours', 'rate', 'cost')
            ->withTimestamps();
    }
    // /////////////////////////////////////////////////////////////////////////////////
}
