@extends('layouts.admin')

@section('title', 'Field Details - Field Management System')

@section('content')
    <!-- <PERSON> Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <h1 class="page-title fw-semibold fs-18 mb-0">Field Details - {{ $field->name }}</h1>
        <div class="ms-md-1 ms-0">
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.fields.index') }}">Fields</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Details</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- <PERSON> Header Close -->

    <div class="row">
        <div class="col-xl-12">
            <!-- Success/Error Messages -->
            @if (session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @endif

            @if (session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    {{ session('error') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @endif

            <div class="card custom-card">
                <div class="card-header justify-content-between">
                    <div class="card-title">Field Information</div>
                    <div class="d-flex gap-2">
                        <a href="{{ route('admin.fields.edit', $field) }}" class="btn btn-sm btn-primary">
                            <i class="ri-edit-line me-1"></i>Edit Field
                        </a>
                        <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal"
                            data-bs-target="#deleteModal">
                            <i class="ri-delete-bin-line me-1"></i>Delete Field
                        </button>
                        <a href="{{ route('admin.fields.index') }}" class="btn btn-sm btn-secondary">
                            <i class="ri-arrow-left-line me-1"></i>Back to Fields
                        </a>
                    </div>
                </div>
                <div class="card-body">

                    <div class="row">
                        <!-- Basic Information -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Basic Information</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row mb-3">
                                        <div class="col-sm-4"><strong>Field Name:</strong></div>
                                        <div class="col-sm-8">{{ $field->name }}</div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-sm-4"><strong>Type:</strong></div>
                                        <div class="col-sm-8">
                                            <span class="badge bg-primary">{{ $field->type }}</span>
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-sm-4"><strong>Description:</strong></div>
                                        <div class="col-sm-8">{{ $field->description ?: 'No description provided' }}</div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-sm-4"><strong>Status:</strong></div>
                                        <div class="col-sm-8">
                                            <span
                                                class="badge
                                                @if ($field->status === 'Active') bg-success
                                                @elseif($field->status === 'Under Maintenance') bg-warning
                                                @else bg-danger @endif">
                                                {{ $field->status }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Pricing and Capacity -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Pricing & Capacity</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row mb-3">
                                        <div class="col-sm-4"><strong>Day Rate:</strong></div>
                                        <div class="col-sm-8">{{ $field->formatted_day_rate }}</div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-sm-4"><strong>Night Rate:</strong></div>
                                        <div class="col-sm-8">{{ $field->formatted_night_rate }}</div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-sm-4"><strong>Night Time Starts:</strong></div>
                                        <div class="col-sm-8">{{ $field->formatted_night_time_start }}</div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-sm-4"><strong>Maximum Capacity:</strong></div>
                                        <div class="col-sm-8">{{ $field->capacity }} people</div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-sm-4"><strong>Created:</strong></div>
                                        <div class="col-sm-8">{{ $field->created_at->format('M d, Y H:i') }}</div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-sm-4"><strong>Last Updated:</strong></div>
                                        <div class="col-sm-8">{{ $field->updated_at->format('M d, Y H:i') }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {{-- Amenities section removed --}}
                    {{--
                    <div class="row mt-3">
                        <div class="col-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Available Amenities</h5>
                                </div>
                                <div class="card-body">
                                    @if ($field->amenities && (is_array($field->amenities) ? count($field->amenities) > 0 : $field->amenities->count() > 0))
                                        <div class="row">
                                            <ul class="list-unstyled">
                                                @foreach ($field->amenities as $amenity)
                                                    <li>
                                                        <i class="{{ $amenity->icon_class ?? 'ri-checkbox-circle-line' }} me-1 text-success"></i>
                                                        {{ $amenity->name }}
                                                    </li>
                                                @endforeach
                                            </ul>
                                        </div>
                                    @else
                                        <p class="text-muted">No amenities specified</p>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                    --}}

                                     Utilities
                                    <div class="row mt-3"> --
                            <div class="col-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Available Utilities</h5>
                                    </div>
                                    <div class="card-body">
                                        @if ($field->utilities && $field->utilities->count() > 0)
                                            <div class="row">
                                                <!-- @foreach ($field->utilities as $utility)
    <div class="col-md-3 col-sm-6 mb-2">
                                                                                <span class="badge bg-info">
                                                                                    <i
                                                                                        class="{{ $utility->icon_class ?? 'ri-tools-line' }} me-1"></i>{{ $utility->name }}
                                                                                </span>
                                                                            </div>
    @endforeach--

                                                <ul class="list-unstyled">
                                                    @foreach ($field->utilities as $utility)
    <li>
                                                            <i
                                                                class="{{ $utility->icon_class ?? 'ri-tools-line' }} me-1 text-info"></i>
                                                            {{ $utility->name }}
                                                        </li>
    @endforeach
                                                </ul>
                                            </div>
@else
    <p class="text-muted">No utilities specified</p>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>-->

                    <!-- Recent Bookings -->
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Recent Bookings</h5>
                                </div>
                                <div class="card-body">
                                    @if ($field->bookings->count() > 0)
                                        <div class="table-responsive">
                                            <table class="table table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>Date</th>
                                                        <th>Time</th>
                                                        <th>Customer</th>
                                                        <th>Status</th>
                                                        <th>Cost</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach ($field->bookings as $booking)
                                                        <tr>
                                                            <td>{{ $booking->booking_date->format('M d, Y') }}</td>
                                                            <td>{{ $booking->time_range }}</td>
                                                            <td>{{ $booking->customer_display_name }}</td>
                                                            <td>
                                                                <span class="badge bg-{{ $booking->status_color }}">
                                                                    {{ $booking->status }}
                                                                </span>
                                                            </td>
                                                            <td>XCG {{ number_format($booking->total_cost, 2) }}</td>
                                                        </tr>
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                    @else
                                        <p class="text-muted">No bookings yet</p>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteModalLabel">Delete Field</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center">
                        <div class="mb-3">
                            <i class="ri-error-warning-line text-danger" style="font-size: 3rem;"></i>
                        </div>
                        <p>Are you sure you want to delete <strong id="fieldName">{{ $field->name }}</strong>?</p>
                        <p class="text-muted">This action cannot be undone.</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <form method="POST" action="{{ route('admin.fields.destroy', $field) }}" class="d-inline"
                        id="deleteForm">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger">Delete Field</button>
                    </form>
                </div>
            </div>
        </div>
    </div>


@endsection

@push('scripts')
    <script>
        // Delete confirmation function
        function confirmDelete(fieldId, fieldName) {
            document.getElementById('fieldName').textContent = fieldName;
            document.getElementById('deleteForm').action = `/admin/fields/${fieldId}`;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }

        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                alert.style.transition = 'opacity 0.5s';
                alert.style.opacity = '0';
                setTimeout(() => alert.remove(), 500);
            });
        }, 5000);
    </script>
@endpush
