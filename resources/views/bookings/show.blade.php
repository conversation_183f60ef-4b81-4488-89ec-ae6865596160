@extends('layouts.admin')

@section('title', 'Booking Details - Field Management System')

@section('content')
    <!-- <PERSON> Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <h1 class="page-title fw-semibold fs-18 mb-0">Booking Details</h1>
        <div class="ms-md-1 ms-0">
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('bookings.index') }}">Bookings</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Booking #{{ $booking->id }}</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- Page Header Close -->

    <!-- Success/Error Messages -->
    @if (session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="ti ti-check-circle me-2"></i>{{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if (session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="ti ti-exclamation-triangle me-2"></i>{{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <!-- Booking Overview Card -->
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header justify-content-between">
                    <div class="card-title">
                        <i class="ti ti-calendar-event me-2"></i>Booking #{{ $booking->id }} - {{ $booking->field->name }}
                        @php
                            $statusColors = [
                                'Pending' => 'warning',
                                'Confirmed' => 'success',
                                'Cancelled' => 'danger',
                                'Completed' => 'primary',
                            ];
                            $statusColor = $statusColors[$booking->status] ?? 'secondary';
                        @endphp
                        <span
                            class="badge bg-{{ $statusColor }}-transparent text-{{ $statusColor }} ms-2">{{ $booking->status }}</span>
                    </div>
                    <div class="d-flex gap-2">
                        @if (auth()->user()->isAdmin() ||
                                ($booking->user_id === auth()->id() &&
                                    !in_array($booking->status, ['Completed', 'Cancelled']) &&
                                    $booking->booking_date->isFuture()))
                            <a href="{{ route('bookings.edit', $booking) }}" class="btn btn-primary btn-sm">
                                <i class="ti ti-edit me-1"></i>Edit Booking
                            </a>
                        @endif

                        @if ($booking->canBeCancelled() && (auth()->user()->isAdmin() || $booking->user_id === auth()->id()))
                            <form method="POST" action="{{ route('bookings.cancel', $booking) }}" class="d-inline"
                                onsubmit="return confirm('Are you sure you want to cancel this booking?')">
                                @csrf
                                <button type="submit" class="btn btn-danger btn-sm">
                                    <i class="ti ti-x me-1"></i>Cancel Booking
                                </button>
                            </form>
                        @endif

                        @if (auth()->user()->isAdmin() && $booking->canBeConfirmed())
                            <form method="POST" action="{{ route('bookings.confirm', $booking) }}" class="d-inline">
                                @csrf
                                <button type="submit" class="btn btn-success btn-sm">
                                    <i class="ti ti-check me-1"></i>Confirm Booking
                                </button>
                            </form>
                        @endif

                        <a href="{{ route('bookings.index') }}" class="btn btn-secondary btn-sm">
                            <i class="ti ti-arrow-left me-1"></i>Back to Bookings
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="row gy-4">
        <!-- Booking Details -->
        <div class="col-xl-6">
            <div class="card custom-card h-100">
                <div class="card-header">
                    <div class="card-title">
                        <i class="ti ti-info-circle me-2"></i>Booking Details
                    </div>
                </div>
                <div class="card-body">
                    <div class="row gy-3">
                        <div class="col-xl-12">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="fw-semibold text-muted">Booking ID:</span>
                                <span class="fw-semibold">#{{ $booking->id }}</span>
                            </div>
                        </div>
                        <div class="col-xl-12">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="fw-semibold text-muted">Field:</span>
                                <span class="fw-semibold">{{ $booking->field->name }}</span>
                            </div>
                        </div>
                        <div class="col-xl-12">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="fw-semibold text-muted">Field Type:</span>
                                <span class="badge bg-info-transparent text-info">{{ $booking->field->type }}</span>
                            </div>
                        </div>
                        <div class="col-xl-12">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="fw-semibold text-muted">Date:</span>
                                <span class="fw-semibold">{{ $booking->booking_date->format('l, F j, Y') }}</span>
                            </div>
                        </div>
                        <div class="col-xl-12">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="fw-semibold text-muted">Time:</span>
                                <span class="fw-semibold">{{ $booking->start_time }} - {{ $booking->end_time }}</span>
                            </div>
                        </div>
                        <div class="col-xl-12">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="fw-semibold text-muted">Duration:</span>
                                <span class="fw-semibold">{{ $booking->duration_hours }}
                                    {{ Str::plural('hour', $booking->duration_hours) }}</span>
                            </div>
                        </div>
                        <div class="col-xl-12">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="fw-semibold text-muted">Total Cost:</span>
                                <span class="fw-bold text-success fs-16">XCG
                                    {{ number_format($booking->total_cost, 2) }}</span>
                            </div>
                        </div>
                        <div class="col-xl-12">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="fw-semibold text-muted">Status:</span>
                                <span
                                    class="badge bg-{{ $statusColor }}-transparent text-{{ $statusColor }}">{{ $booking->status }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Customer Information -->
        <div class="col-xl-6">
            <div class="card custom-card h-100">
                <div class="card-header">
                    <div class="card-title">
                        <i class="ti ti-user me-2"></i>Customer Information
                    </div>
                </div>
                <div class="card-body">
                    <div class="row gy-3">
                        <div class="col-xl-12">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="fw-semibold text-muted">Customer Name:</span>
                                <span class="fw-semibold">{{ $booking->customer_display_name }}</span>
                            </div>
                        </div>
                        @if ($booking->customer_email)
                            <div class="col-xl-12">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="fw-semibold text-muted">Email:</span>
                                    <span class="fw-semibold">
                                        <a href="mailto:{{ $booking->customer_email }}"
                                            class="text-primary">{{ $booking->customer_email }}</a>
                                    </span>
                                </div>
                            </div>
                        @endif
                        @if ($booking->customer_phone)
                            <div class="col-xl-12">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="fw-semibold text-muted">Phone:</span>
                                    <span class="fw-semibold">
                                        <a href="tel:{{ $booking->customer_phone }}"
                                            class="text-primary">{{ $booking->customer_phone }}</a>
                                    </span>
                                </div>
                            </div>
                        @endif
                        <div class="col-xl-12">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="fw-semibold text-muted">Booked By:</span>
                                <span class="fw-semibold">{{ $booking->user->name }}</span>
                            </div>
                        </div>
                        @if ($booking->bookedBy && $booking->bookedBy->id !== $booking->user->id)
                            <div class="col-xl-12">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="fw-semibold text-muted">Admin Booked By:</span>
                                    <span class="fw-semibold">{{ $booking->bookedBy->name }}</span>
                                </div>
                            </div>
                        @endif
                        <div class="col-xl-12">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="fw-semibold text-muted">User Role:</span>
                                <span
                                    class="badge bg-primary-transparent text-primary">{{ ucfirst($booking->user->role) }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Information Row -->
    <div class="row gy-4 mt-2">
        <!-- Field Information -->
        <div class="col-xl-6">
            <div class="card custom-card h-100">
                <div class="card-header">
                    <div class="card-title">
                        <i class="ti ti-building-stadium me-2"></i>Field Information
                    </div>
                </div>
                <div class="card-body">
                    <div class="row gy-3">
                        <div class="col-xl-12">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="fw-semibold text-muted">Field Name:</span>
                                <span class="fw-semibold">{{ $booking->field->name }}</span>
                            </div>
                        </div>
                        <div class="col-xl-12">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="fw-semibold text-muted">Type:</span>
                                <span class="badge bg-info-transparent text-info">{{ $booking->field->type }}</span>
                            </div>
                        </div>
                        <div class="col-xl-12">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="fw-semibold text-muted">Capacity:</span>
                                <span class="fw-semibold">{{ $booking->field->capacity }} people</span>
                            </div>
                        </div>
                        <div class="col-xl-12">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="fw-semibold text-muted">Hourly Rate:</span>
                                <span class="fw-semibold text-success">XCG
                                    {{ number_format($booking->field->hourly_rate, 2) }}</span>
                            </div>
                        </div>
                        {{-- @if ($booking->field->amenities && $booking->field->amenities->count() > 0)
                            <div class="col-xl-12">
                                <div class="d-flex justify-content-between align-items-start">
                                    <span class="fw-semibold text-muted">Amenities:</span>
                                    <div class="text-end">
                                        @foreach ($booking->field->amenities as $amenity)
                                            <span class="badge bg-light text-dark me-1">
                                                <i class="{{ $amenity->icon_class ?? 'ri-checkbox-circle-line' }} me-1"></i>{{ ucfirst($amenity->name) }}
                                            </span>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                        @endif --}}
                        @if ($booking->field->description)
                            <div class="col-xl-12">
                                <div class="border-top pt-3">
                                    <span class="fw-semibold text-muted d-block mb-2">Description:</span>
                                    <p class="text-muted mb-0">{{ $booking->field->description }}</p>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Booking Timeline -->
        <div class="col-xl-6">
            <div class="card custom-card h-100">
                <div class="card-header">
                    <div class="card-title">
                        <i class="ti ti-clock-history me-2"></i>Booking Timeline
                    </div>
                </div>
                <div class="card-body">
                    <div class="timeline-steps">
                        <div class="timeline-step">
                            <div class="timeline-step-icon bg-primary">
                                <i class="ti ti-plus"></i>
                            </div>
                            <div class="timeline-step-content">
                                <h6 class="fw-semibold mb-1">Booking Created</h6>
                                <p class="text-muted mb-0">{{ $booking->created_at->format('M d, Y \a\t H:i') }}</p>
                            </div>
                        </div>

                        @if ($booking->confirmed_at)
                            <div class="timeline-step">
                                <div class="timeline-step-icon bg-success">
                                    <i class="ti ti-check"></i>
                                </div>
                                <div class="timeline-step-content">
                                    <h6 class="fw-semibold mb-1">Booking Confirmed</h6>
                                    <p class="text-muted mb-0">{{ $booking->confirmed_at->format('M d, Y \a\t H:i') }}</p>
                                </div>
                            </div>
                        @endif

                        @if ($booking->cancelled_at)
                            <div class="timeline-step">
                                <div class="timeline-step-icon bg-danger">
                                    <i class="ti ti-x"></i>
                                </div>
                                <div class="timeline-step-content">
                                    <h6 class="fw-semibold mb-1">Booking Cancelled</h6>
                                    <p class="text-muted mb-0">{{ $booking->cancelled_at->format('M d, Y \a\t H:i') }}</p>
                                </div>
                            </div>
                        @endif

                        <div class="timeline-step">
                            <div class="timeline-step-icon bg-secondary">
                                <i class="ti ti-edit"></i>
                            </div>
                            <div class="timeline-step-content">
                                <h6 class="fw-semibold mb-1">Last Updated</h6>
                                <p class="text-muted mb-0">{{ $booking->updated_at->format('M d, Y \a\t H:i') }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Special Requests & Notes -->
    @if ($booking->special_requests || ($booking->admin_notes && auth()->user()->isAdmin()))
        <div class="row mt-4">
            <div class="col-xl-12">
                <div class="card custom-card">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="ti ti-notes me-2"></i>Additional Information
                        </div>
                    </div>
                    <div class="card-body">
                        @if ($booking->special_requests)
                            <div class="mb-4">
                                <h6 class="fw-semibold mb-2">Special Requests:</h6>
                                <div class="alert alert-info">
                                    <i class="ti ti-info-circle me-2"></i>{{ $booking->special_requests }}
                                </div>
                            </div>
                        @endif
                        @if ($booking->admin_notes && auth()->user()->isAdmin())
                            <div>
                                <h6 class="fw-semibold mb-2">Admin Notes:</h6>
                                <div class="alert alert-warning">
                                    <i class="ti ti-shield-lock me-2"></i>{{ $booking->admin_notes }}
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    @endif
@endsection

@push('styles')
    <style>
        /* Timeline Styles */
        .timeline-steps {
            position: relative;
        }

        .timeline-step {
            display: flex;
            align-items: flex-start;
            margin-bottom: 1.5rem;
            position: relative;
        }

        .timeline-step:not(:last-child)::after {
            content: '';
            position: absolute;
            left: 15px;
            top: 32px;
            width: 2px;
            height: calc(100% + 0.5rem);
            background-color: #e9ecef;
            z-index: 1;
        }

        .timeline-step-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            position: relative;
            z-index: 2;
            flex-shrink: 0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .timeline-step-icon i {
            font-size: 14px;
            color: white;
        }

        .timeline-step-content {
            flex: 1;
            padding-top: 2px;
        }

        .timeline-step-content h6 {
            margin-bottom: 0.25rem;
            font-size: 0.875rem;
            color: #495057;
        }

        .timeline-step-content p {
            font-size: 0.75rem;
            margin-bottom: 0;
            color: #6c757d;
        }

        /* Card Enhancements */
        .custom-card {
            border: 1px solid #e9ecef;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            transition: box-shadow 0.15s ease-in-out;
        }

        .custom-card:hover {
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }

        /* Status Badge Enhancements */
        .badge {
            font-weight: 500;
            letter-spacing: 0.025em;
        }

        /* Responsive Improvements */
        @media (max-width: 768px) {
            .d-flex.gap-2 {
                flex-direction: column;
                gap: 0.5rem !important;
            }

            .btn-sm {
                width: 100%;
                justify-content: center;
            }

            .timeline-step {
                margin-bottom: 1rem;
            }

            .timeline-step-icon {
                width: 28px;
                height: 28px;
            }

            .timeline-step-icon i {
                font-size: 12px;
            }
        }

        /* Print Styles */
        @media print {

            .btn,
            .card-header .d-flex {
                display: none !important;
            }

            .custom-card {
                border: 1px solid #000 !important;
                box-shadow: none !important;
            }

            .page-header-breadcrumb {
                display: none !important;
            }
        }

        /* Enhanced Visual Elements */
        .fw-bold.text-success {
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .alert {
            border-left: 4px solid;
        }

        .alert-info {
            border-left-color: #0dcaf0;
        }

        .alert-warning {
            border-left-color: #ffc107;
        }
    </style>
@endpush
