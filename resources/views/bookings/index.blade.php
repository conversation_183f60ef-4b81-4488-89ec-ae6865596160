@extends('layouts.admin')

@section('title', 'Bookings - Field Management System')

@section('content')
    <!-- Page Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <h1 class="page-title fw-semibold fs-18 mb-0">
            @if (auth()->user()->isAdmin())
                All Bookings
            @else
                My Bookings
            @endif
        </h1>
        <div class="ms-md-1 ms-0">
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Bookings</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- Page Header Close -->

    <!-- Success/Error Messages -->
    @if (session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle me-2"></i>{{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if (session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>{{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <!-- Bookings Table Card -->
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header justify-content-between">
                    <div class="card-title">
                        @if (auth()->user()->isAdmin())
                            All Bookings
                        @else
                            My Bookings
                        @endif
                    </div>
                    <div class="d-flex gap-2">
                        <a href="{{ route('bookings.create') }}" class="btn btn-primary btn-sm">
                            <i class="ti ti-plus me-1"></i>New Booking
                        </a>
                        <a href="{{ route('calendar.index') }}" class="btn btn-secondary btn-sm">
                            <i class="ti ti-calendar me-1"></i>Calendar View
                        </a>
                    </div>
                </div>
                <div class="card-body">

                    <!-- Search and Filter Form -->
                    <form method="GET" action="{{ route('bookings.index') }}" class="mb-4">
                        <div class="row gy-3">
                            <div class="col-xl-2">
                                <select name="status" class="form-select">
                                    <option value="">All Statuses</option>
                                    @foreach (\App\Models\Booking::getStatuses() as $key => $status)
                                        <option value="{{ $key }}"
                                            {{ request('status') === $key ? 'selected' : '' }}>{{ $status }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-xl-3">
                                <select name="field_id" class="form-select">
                                    <option value="">All Fields</option>
                                    @foreach ($fields as $field)
                                        <option value="{{ $field->id }}"
                                            {{ request('field_id') == $field->id ? 'selected' : '' }}>{{ $field->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-xl-2">
                                <input type="date" name="date_from" value="{{ request('date_from') }}"
                                    placeholder="From Date" class="form-control">
                            </div>
                            <div class="col-xl-2">
                                <input type="date" name="date_to" value="{{ request('date_to') }}" placeholder="To Date"
                                    class="form-control">
                            </div>
                            <div class="col-xl-3">
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary flex-fill">
                                        <i class="ti ti-search me-1"></i>Filter
                                    </button>
                                    <a href="{{ route('bookings.index') }}" class="btn btn-secondary flex-fill">
                                        <i class="ti ti-refresh me-1"></i>Clear
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- Bookings Table -->
                    <div class="table-responsive">
                        <table class="table text-nowrap table-striped table-hover" id="bookingsTable">
                            <thead>
                                <tr>
                                    <th scope="col">Booking Details</th>
                                    <th scope="col">Field</th>
                                    <th scope="col">Date & Time</th>
                                    <th scope="col">Duration</th>
                                    <th scope="col">Cost</th>
                                    <th scope="col">Status</th>
                                    <th scope="col">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse ($bookings as $booking)
                                    <tr>
                                        <td>
                                            <div>
                                                <div class="fw-semibold">{{ $booking->customer_display_name }}</div>
                                                @if ($booking->customer_email)
                                                    <div class="text-muted fs-12">{{ $booking->customer_email }}</div>
                                                @endif
                                                @if ($booking->customer_phone)
                                                    <div class="text-muted fs-12">{{ $booking->customer_phone }}</div>
                                                @endif
                                            </div>
                                        </td>
                                        <td>
                                            <div class="fw-semibold">{{ $booking->field->name }}</div>
                                            <div class="text-muted fs-12">{{ $booking->field->type }}</div>
                                        </td>
                                        <td>
                                            <div>{{ $booking->booking_date->format('M d, Y') }}</div>
                                            <div class="text-muted fs-12">{{ $booking->time_range }}</div>
                                        </td>
                                        <td>{{ $booking->duration_hours }}
                                            {{ Str::plural('hour', $booking->duration_hours) }}</td>
                                        <td>XCG {{ number_format($booking->total_cost, 2) }}</td>
                                        <td>
                                            @if ($booking->status === 'confirmed')
                                                <span class="badge bg-success-transparent">{{ $booking->status }}</span>
                                            @elseif($booking->status === 'pending')
                                                <span class="badge bg-warning-transparent">{{ $booking->status }}</span>
                                            @else
                                                <span class="badge bg-danger-transparent">{{ $booking->status }}</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="hstack gap-2 fs-15">
                                                <a href="{{ route('bookings.show', $booking) }}"
                                                    class="btn btn-icon btn-sm btn-info-transparent rounded-pill"
                                                    data-bs-toggle="tooltip" data-bs-placement="top" title="View">
                                                    <i class="ri-eye-line"></i>
                                                </a>

                                                @if (auth()->user()->isAdmin() ||
                                                        ($booking->user_id === auth()->id() &&
                                                            !in_array($booking->status, ['Completed', 'Cancelled']) &&
                                                            $booking->booking_date->isFuture()))
                                                    <a href="{{ route('bookings.edit', $booking) }}"
                                                        class="btn btn-icon btn-sm btn-primary-transparent rounded-pill"
                                                        data-bs-toggle="tooltip" data-bs-placement="top" title="Edit">
                                                        <i class="ri-pencil-line"></i>
                                                    </a>
                                                @endif

                                                @if (auth()->user()->isAdmin() && $booking->canBeConfirmed())
                                                    <form method="POST"
                                                        action="{{ route('bookings.confirm', $booking) }}"
                                                        class="d-inline">
                                                        @csrf
                                                        <button type="submit"
                                                            class="btn btn-icon btn-sm btn-success-transparent rounded-pill"
                                                            data-bs-toggle="tooltip" data-bs-placement="top"
                                                            title="Confirm">
                                                            <i class="ri-check-line"></i>
                                                        </button>
                                                    </form>
                                                @endif

                                                @if ($booking->canBeCancelled() && (auth()->user()->isAdmin() || $booking->user_id === auth()->id()))
                                                    <form method="POST"
                                                        action="{{ route('bookings.cancel', $booking) }}"
                                                        class="d-inline"
                                                        onsubmit="return confirm('Are you sure you want to cancel this booking?')">
                                                        @csrf
                                                        <button type="submit"
                                                            class="btn btn-icon btn-sm btn-danger-transparent rounded-pill"
                                                            data-bs-toggle="tooltip" data-bs-placement="top"
                                                            title="Cancel">
                                                            <i class="ri-close-line"></i>
                                                        </button>
                                                    </form>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="7" class="text-center">
                                            No bookings found. <a href="{{ route('bookings.create') }}"
                                                class="text-primary">Create your first booking</a>.
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card-footer booking-pagination">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="pagination-info">
                            Showing {{ $bookings->firstItem() ?? 0 }} to {{ $bookings->lastItem() ?? 0 }} of
                            {{ $bookings->total() }} entries
                        </div>
                        <div class="admin-pagination">
                            {{ $bookings->withQueryString()->links() }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- DataTables Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        });
    </script>
@endsection
