<?php

namespace Tests\Feature;

use App\Models\Amenity;
use App\Models\Field;
use App\Models\Reservation;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class AmenityFieldBladeIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected User $admin;

    protected User $client;

    protected function setUp(): void
    {
        parent::setUp();

        $this->admin = User::factory()->create(['role' => 'admin']);
        $this->client = User::factory()->create(['role' => 'user']);
    }

    #[Test]
    public function bookings_show_page_renders_without_amenities_display()
    {
        // Create field with amenities
        $field = Field::factory()->create(['name' => 'Test Field']);
        $amenity1 = Amenity::factory()->create([
            'name' => 'Lighting',
            'icon_class' => 'ri-lightbulb-line',
            'is_active' => true,
        ]);
        $amenity2 = Amenity::factory()->create([
            'name' => 'Parking',
            'icon_class' => 'ri-car-line',
            'is_active' => true,
        ]);

        $field->amenities()->attach([$amenity1->id, $amenity2->id]);

        // Create booking/reservation
        $booking = Reservation::factory()->create([
            'field_id' => $field->id,
            'user_id' => $this->client->id,
        ]);

        // Test the view renders without errors
        $response = $this->actingAs($this->client)
            ->get(route('bookings.show', $booking));

        $response->assertStatus(200);

        // Verify amenities are NOT displayed (feature was removed)
        $response->assertDontSee('Amenities:');

        // Verify no PHP errors in the response
        $response->assertDontSee('TypeError');
        $response->assertDontSee('Cannot access offset');
        $response->assertDontSee('Array to string conversion');
    }

    #[Test]
    public function bookings_show_page_handles_field_without_amenities()
    {
        // Create field without amenities
        $field = Field::factory()->create(['name' => 'Basic Field']);

        // Create booking
        $booking = Reservation::factory()->create([
            'field_id' => $field->id,
            'user_id' => $this->client->id,
        ]);

        // Test the view renders without errors
        $response = $this->actingAs($this->client)
            ->get(route('bookings.show', $booking));

        $response->assertStatus(200);

        // Should not show amenities section when there are none
        $response->assertDontSee('Amenities:');

        // Verify no PHP errors
        $response->assertDontSee('TypeError');
        $response->assertDontSee('Cannot access offset');
    }

    #[Test]
    public function admin_fields_show_page_renders_amenities_correctly()
    {
        // Create field with amenities
        $field = Field::factory()->create(['name' => 'Admin Test Field']);
        $amenity1 = Amenity::factory()->create([
            'name' => 'WiFi',
            'icon_class' => 'ri-wifi-line',
            'is_active' => true,
        ]);
        $amenity2 = Amenity::factory()->create([
            'name' => 'Sound System',
            'icon_class' => 'ri-volume-up-line',
            'is_active' => true,
        ]);

        $field->amenities()->attach([$amenity1->id, $amenity2->id]);

        // Test admin view
        $response = $this->actingAs($this->admin)
            ->get(route('admin.fields.show', $field));

        $response->assertStatus(200);

        // Verify amenities section does not exist (feature removed)
        $response->assertDontSee('Available Amenities');

        // Verify no PHP errors
        $response->assertDontSee('TypeError');
        $response->assertDontSee('Cannot access offset');
    }

    #[Test]
    public function admin_fields_show_page_handles_field_without_amenities()
    {
        // Create field without amenities
        $field = Field::factory()->create(['name' => 'Empty Field']);

        // Test admin view
        $response = $this->actingAs($this->admin)
            ->get(route('admin.fields.show', $field));

        $response->assertStatus(200);

        // Should show "No amenities specified" message
        $response->assertSee('No amenities specified');

        // Verify no PHP errors
        $response->assertDontSee('TypeError');
        $response->assertDontSee('Cannot access offset');
    }

    #[Test]
    public function amenities_display_with_proper_object_property_access()
    {
        // Create field with amenity that has all properties
        $field = Field::factory()->create();
        $amenity = Amenity::factory()->create([
            'name' => 'Complete Amenity',
            'description' => 'Full description',
            'icon_class' => 'ri-star-line',
            'is_active' => true,
        ]);

        $field->amenities()->attach($amenity->id);

        // Create booking
        $booking = Reservation::factory()->create([
            'field_id' => $field->id,
            'user_id' => $this->client->id,
        ]);

        $response = $this->actingAs($this->client)
            ->get(route('bookings.show', $booking));

        $response->assertStatus(200);

        // Verify amenities are not displayed (feature removed)
        $response->assertDontSee('Complete Amenity');
        $response->assertDontSee('ri-star-line');

        // The response should not contain any array-style access attempts
        $content = $response->getContent();
        $this->assertStringNotContainsString('$amenity[', $content);
        $this->assertStringNotContainsString('$availableAmenities[$amenity]', $content);
    }

    #[Test]
    public function multiple_amenities_render_correctly_in_loop()
    {
        // Create field with multiple amenities
        $field = Field::factory()->create();
        $amenities = [
            Amenity::factory()->create(['name' => 'Amenity 1', 'icon_class' => 'ri-1-line']),
            Amenity::factory()->create(['name' => 'Amenity 2', 'icon_class' => 'ri-2-line']),
            Amenity::factory()->create(['name' => 'Amenity 3', 'icon_class' => 'ri-3-line']),
        ];

        $field->amenities()->attach($amenities);

        // Create booking
        $booking = Reservation::factory()->create([
            'field_id' => $field->id,
            'user_id' => $this->client->id,
        ]);

        $response = $this->actingAs($this->client)
            ->get(route('bookings.show', $booking));

        $response->assertStatus(200);

        // Verify amenities are not displayed (feature removed)
        foreach ($amenities as $amenity) {
            $response->assertDontSee($amenity->name);
            $response->assertDontSee($amenity->icon_class);
        }

        // Verify no errors occurred during the foreach loop
        $response->assertDontSee('TypeError');
        $response->assertDontSee('Cannot access offset');
    }

    #[Test]
    public function amenity_icons_display_correctly_with_custom_icon()
    {
        // Create amenity with custom icon_class
        $field = Field::factory()->create();
        $amenity = Amenity::factory()->create([
            'name' => 'Custom Icon Amenity',
            'icon_class' => 'ri-star-line', // Custom icon
        ]);

        $field->amenities()->attach($amenity->id);

        // Create booking
        $booking = Reservation::factory()->create([
            'field_id' => $field->id,
            'user_id' => $this->client->id,
        ]);

        $response = $this->actingAs($this->client)
            ->get(route('bookings.show', $booking));

        $response->assertStatus(200);

        // Amenities are not displayed (feature removed)
        $response->assertDontSee('ri-star-line'); // custom icon
        $response->assertDontSee('Custom Icon Amenity');

        // No errors should occur
        $response->assertDontSee('TypeError');
    }

    #[Test]
    public function inactive_amenities_are_not_displayed_in_available_amenities()
    {
        // Create field with both active and inactive amenities
        $field = Field::factory()->create();
        $activeAmenity = Amenity::factory()->create([
            'name' => 'Active Amenity',
            'is_active' => true,
        ]);
        $inactiveAmenity = Amenity::factory()->create([
            'name' => 'Inactive Amenity',
            'is_active' => false,
        ]);

        // Attach both (though inactive shouldn't normally be attached)
        $field->amenities()->attach([$activeAmenity->id, $inactiveAmenity->id]);

        // Create booking
        $booking = Reservation::factory()->create([
            'field_id' => $field->id,
            'user_id' => $this->client->id,
        ]);

        $response = $this->actingAs($this->client)
            ->get(route('bookings.show', $booking));

        $response->assertStatus(200);

        // Both should be displayed since they're attached to the field
        // (the is_active flag is for availability, not display)
        $response->assertSee('Active Amenity');
        $response->assertSee('Inactive Amenity');
    }

    #[Test]
    public function blade_template_handles_edge_case_with_empty_amenity_name()
    {
        // Create amenity with empty name (edge case)
        $field = Field::factory()->create();
        $amenity = Amenity::factory()->create([
            'name' => '',
            'icon_class' => 'ri-question-line',
        ]);

        $field->amenities()->attach($amenity->id);

        // Create booking
        $booking = Reservation::factory()->create([
            'field_id' => $field->id,
            'user_id' => $this->client->id,
        ]);

        $response = $this->actingAs($this->client)
            ->get(route('bookings.show', $booking));

        $response->assertStatus(200);

        // Should still render without errors (amenities not displayed)
        $response->assertDontSee('ri-question-line');
        $response->assertDontSee('TypeError');
    }
}
