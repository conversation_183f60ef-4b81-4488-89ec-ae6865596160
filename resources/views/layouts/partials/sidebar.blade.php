<aside class="app-sidebar sticky" id="sidebar">

    <!-- Start::main-sidebar-header -->
    <div class="main-sidebar-header">
        <a href="{{ route('dashboard') }}" class="header-logo">
            <img src="{{ asset('assets/images/brand-logos/desktop-logo.png') }}" alt="logo" class="desktop-logo">
            <img src="{{ asset('assets/images/brand-logos/toggle-logo.png') }}" alt="logo" class="toggle-logo">
            <img src="{{ asset('assets/images/brand-logos/desktop-dark.png') }}" alt="logo" class="desktop-dark">
            <img src="{{ asset('assets/images/brand-logos/toggle-dark.png') }}" alt="logo" class="toggle-dark">
        </a>
    </div>
    <!-- End::main-sidebar-header -->

    <!-- Start::main-sidebar -->
    <div class="main-sidebar" id="sidebar-scroll">

        <!-- Start::nav -->
        <nav class="main-menu-container nav nav-pills flex-column sub-open">
            <div class="slide-left" id="slide-left">
                <svg xmlns="http://www.w3.org/2000/svg" fill="#7b8191" width="24" height="24"
                    viewBox="0 0 24 24">
                    <path d="M13.293 6.293 7.586 12l5.707 5.707 1.414-1.414L10.414 12l4.293-4.293z"></path>
                </svg>
            </div>
            <ul class="main-menu">
                <!-- Start::slide__category -->
                <li class="slide__category"><span class="category-name">Main</span></li>
                <!-- End::slide__category -->

                <!-------------------------------------------------------------------------------------------------------->
                @php
                    if (auth()->user()?->isAdmin()) {
                        $dashboardRouteName = 'admin.dashboard';
                    } elseif (auth()->user()?->isEmployee()) {
                        $dashboardRouteName = 'employee.dashboard';
                    } elseif (auth()->user()?->isUser()) {
                        $dashboardRouteName = 'user.dashboard';
                    } else {
                        $dashboardRouteName = '';
                    }
                @endphp
                <!-------------------------------------------------------------------------------------------------------->

                <!-- Start::slide -->
                <li class="slide">
                    <a href="{{ route('dashboard') }}"
                        class="side-menu__item {{ request()->routeIs($dashboardRouteName) ? 'active' : '' }}">

                        <i class="bx bx-home side-menu__icon"></i>
                        <span class="side-menu__label">Dashboard</span>
                    </a>
                </li>
                <!-- End::slide -->

                @if (auth()->user()?->isEmployee() || auth()->user()?->isAdmin())
                    <!-- Start::slide -->
                    <li class="slide">
                        <a href="{{ route('employee.features') }}"
                            class="side-menu__item {{ request()->routeIs('employee.features') ? 'active' : '' }}">
                            <i class="bx bx-star side-menu__icon"></i>
                            <span class="side-menu__label">Features</span>
                        </a>
                    </li>
                    <!-- End::slide -->
                @endif

                <!-- Start::slide__category -->
                <li class="slide__category"><span class="category-name">Booking Management</span></li>
                <!-- End::slide__category -->

                <!-- Start::slide -->
                <li class="slide">
                    <a href="{{ route('calendar.index') }}"
                        class="side-menu__item {{ request()->routeIs('calendar.*') ? 'active' : '' }}">
                        <i class="bx bx-calendar side-menu__icon"></i>
                        <span class="side-menu__label">Calendar</span>
                    </a>
                </li>
                <!-- End::slide -->

                @if (auth()->user()?->isEmployee() || auth()->user()?->isAdmin())
                    <!-- Start::slide -->
                    <li class="slide">
                        <a href="{{ route('reservations.create') }}"
                            class="side-menu__item {{ request()->routeIs('reservations.create') ? 'active' : '' }}">
                            <i class="bx bx-plus-circle side-menu__icon"></i>
                            <span class="side-menu__label">Quick Reserve</span>
                            <span class="badge bg-primary ms-auto">FPMP</span>
                        </a>
                    </li>
                    <!-- End::slide -->

                    <!-- Start::slide -->
                    <li class="slide has-sub {{ request()->routeIs('reservations.*') ? 'open' : '' }}">
                        <a href="javascript:void(0);" class="side-menu__item">
                            <i class="bx bx-calendar-event side-menu__icon"></i>
                            <span class="side-menu__label">FPMP Reservations</span>
                            <i class="fe fe-chevron-right side-menu__angle"></i>
                        </a>
                        <ul class="slide-menu child1">
                            <li class="slide side-menu__label1">
                                <a href="javascript:void(0)">FPMP Reservations</a>
                            </li>
                            <li class="slide">
                                <a href="{{ route('reservations.index') }}"
                                    class="side-menu__item {{ request()->routeIs('reservations.index') ? 'active' : '' }}">My
                                    Reservations</a>
                            </li>
                            <li class="slide">
                                <a href="{{ route('reservations.create') }}"
                                    class="side-menu__item {{ request()->routeIs('reservations.create') ? 'active' : '' }}">New
                                    Reservation</a>
                            </li>
                        </ul>
                    </li>
                    <!-- End::slide -->
                @endif

                <!-- Start::slide -->
                <li class="slide has-sub {{ request()->routeIs('bookings.*') ? 'open' : '' }}">
                    <a href="javascript:void(0);" class="side-menu__item">
                        <i class="bx bx-book-bookmark side-menu__icon"></i>
                        <span class="side-menu__label">Admin Bookings</span>
                        <i class="fe fe-chevron-right side-menu__angle"></i>
                    </a>
                    <ul class="slide-menu child1">
                        <li class="slide side-menu__label1">
                            <a href="javascript:void(0)">Admin Bookings</a>
                        </li>
                        <li class="slide">
                            <a href="{{ route('bookings.index') }}"
                                class="side-menu__item {{ request()->routeIs('bookings.index') ? 'active' : '' }}">All
                                Bookings</a>
                        </li>
                        <li class="slide">
                            <a href="{{ route('bookings.create') }}"
                                class="side-menu__item {{ request()->routeIs('bookings.create') ? 'active' : '' }}">Create
                                Booking</a>
                        </li>
                    </ul>
                </li>
                <!-- End::slide -->

                @if (auth()->user()?->isAdmin())
                    <!-- Start::slide__category -->
                    <li class="slide__category"><span class="category-name">Administration</span></li>
                    <!-- End::slide__category -->

                    <!-- Start::slide -->
                    <li class="slide has-sub {{ request()->routeIs('admin.users.*') ? 'open' : '' }}">
                        <a href="javascript:void(0);" class="side-menu__item">
                            <i class="bx bx-user side-menu__icon"></i>
                            <span class="side-menu__label">User Management</span>
                            <i class="fe fe-chevron-right side-menu__angle"></i>
                        </a>
                        <ul class="slide-menu child1">
                            <li class="slide side-menu__label1">
                                <a href="javascript:void(0)">User Management</a>
                            </li>
                            <li class="slide">
                                <a href="{{ route('admin.users.index') }}"
                                    class="side-menu__item {{ request()->routeIs('admin.users.index') ? 'active' : '' }}">All
                                    Users</a>
                            </li>
                            <li class="slide">
                                <a href="{{ route('admin.users.create') }}"
                                    class="side-menu__item {{ request()->routeIs('admin.users.create') ? 'active' : '' }}">Create
                                    User</a>
                            </li>
                        </ul>
                    </li>
                    <!-- End::slide -->

                    <!-- Start::slide -->
                    <li class="slide has-sub {{ request()->routeIs('admin.fields.*') ? 'open' : '' }}">
                        <a href="javascript:void(0);" class="side-menu__item">
                            <i class="bx bx-map side-menu__icon"></i>
                            <span class="side-menu__label">Field Management</span>
                            <i class="fe fe-chevron-right side-menu__angle"></i>
                        </a>
                        <ul class="slide-menu child1">
                            <li class="slide side-menu__label1">
                                <a href="javascript:void(0)">Field Management</a>
                            </li>
                            <li class="slide">
                                <a href="{{ route('admin.fields.index') }}"
                                    class="side-menu__item {{ request()->routeIs('admin.fields.index') ? 'active' : '' }}">All
                                    Fields</a>
                            </li>
                            <li class="slide">
                                <a href="{{ route('admin.fields.create') }}"
                                    class="side-menu__item {{ request()->routeIs('admin.fields.create') ? 'active' : '' }}">Create
                                    Field</a>
                            </li>
                        </ul>
                    </li>
                    <!-- End::slide -->

                    <!-- Start::slide -->
                    <!--<li class="slide has-sub {{ request()->routeIs('admin.amenities.*') ? 'open' : '' }}">
                        <a href="javascript:void(0);" class="side-menu__item">
                            <i class="bx bx-star side-menu__icon"></i>
                            <span class="side-menu__label">Amenity Management</span>
                            <i class="fe fe-chevron-right side-menu__angle"></i>
                        </a>
                        <ul class="slide-menu child1">
                            <li class="slide side-menu__label1">
                                <a href="javascript:void(0)">Amenity Management</a>
                            </li>
                            <li class="slide">
                                <a href="{{ route('admin.amenities.index') }}"
                                    class="side-menu__item {{ request()->routeIs('admin.amenities.index') ? 'active' : '' }}">All
                                    Amenities</a>
                            </li>
                            <li class="slide">
                                <a href="{{ route('admin.amenities.create') }}"
                                    class="side-menu__item {{ request()->routeIs('admin.amenities.create') ? 'active' : '' }}">Create
                                    Amenity</a>
                            </li>
                        </ul>
                    </li>-->
                    <!-- End::slide -->

                    <!-- Start::slide -->
                    <li class="slide has-sub {{ request()->routeIs('admin.utilities.*') ? 'open' : '' }}">
                        <a href="javascript:void(0);" class="side-menu__item">
                            <i class="bx bx-wrench side-menu__icon"></i>
                            <span class="side-menu__label">Utility Management</span>
                            <i class="fe fe-chevron-right side-menu__angle"></i>
                        </a>
                        <ul class="slide-menu child1">
                            <li class="slide side-menu__label1">
                                <a href="javascript:void(0)">Utility Management</a>
                            </li>
                            <li class="slide">
                                <a href="{{ route('admin.utilities.index') }}"
                                    class="side-menu__item {{ request()->routeIs('admin.utilities.index') ? 'active' : '' }}">All
                                    Utilities</a>
                            </li>
                            <li class="slide">
                                <a href="{{ route('admin.utilities.create') }}"
                                    class="side-menu__item {{ request()->routeIs('admin.utilities.create') ? 'active' : '' }}">Create
                                    Utility</a>
                            </li>
                        </ul>
                    </li>
                    <!-- End::slide -->
                @endif

                <!-- Start::slide__category -->
                <li class="slide__category"><span class="category-name">Account</span></li>
                <!-- End::slide__category -->

                <!-- Start::slide -->
                <li class="slide">
                    <a href="{{ route('profile.edit') }}"
                        class="side-menu__item {{ request()->routeIs('profile.*') ? 'active' : '' }}">
                        <i class="bx bx-user-circle side-menu__icon"></i>
                        <span class="side-menu__label">Profile</span>
                    </a>
                </li>
                <!-- End::slide -->

                <!-- Start::slide -->
                <li class="slide">
                    <form method="POST" action="{{ route('logout') }}">
                        @csrf
                        <a href="{{ route('logout') }}" class="side-menu__item"
                            onclick="event.preventDefault(); this.closest('form').submit();">
                            <i class="bx bx-log-out side-menu__icon"></i>
                            <span class="side-menu__label">Logout</span>
                        </a>
                    </form>
                </li>
                <!-- End::slide -->

            </ul>
            <div class="slide-right" id="slide-right"><svg xmlns="http://www.w3.org/2000/svg" fill="#7b8191"
                    width="24" height="24" viewBox="0 0 24 24">
                    <path d="M10.707 17.707 16.414 12l-5.707-5.707-1.414 1.414L13.586 12l-4.293 4.293z"></path>
                </svg></div>
        </nav>
        <!-- End::nav -->

    </div>
    <!-- End::main-sidebar -->
</aside>
